# 数据库设计文档

## 数据库概述

### 基本信息
- **数据库名称**：szrxcx
- **数据库类型**：MySQL 8.0
- **字符集**：utf8mb4
- **表前缀**：fa_
- **时间戳格式**：Unix时间戳（整型）

### 设计原则
1. **统一命名规范**：所有表使用fa_前缀，字段名使用小写+下划线
2. **时间戳字段**：createtime（创建时间）、updatetime（更新时间）
3. **软删除支持**：部分表支持deletetime字段实现软删除
4. **多租户设计**：通过admin_id、oem_id、agent_id实现数据隔离
5. **关联关系**：使用外键关联，支持级联查询

## 核心数据表

### 1. 管理员表 (fa_admin)
**表说明**：系统管理员表，支持多租户架构

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| username | varchar | 20 | NO | | 用户名 |
| nickname | varchar | 50 | NO | | 昵称 |
| password | varchar | 32 | NO | | 密码（加密） |
| salt | varchar | 30 | NO | | 密码盐值 |
| avatar | varchar | 255 | YES | | 头像地址 |
| email | varchar | 100 | YES | | 邮箱 |
| mobile | varchar | 11 | YES | | 手机号 |
| loginfailure | tinyint | 1 | NO | 0 | 登录失败次数 |
| logintime | int | 10 | YES | | 登录时间 |
| loginip | varchar | 50 | YES | | 登录IP |
| createtime | int | 10 | YES | | 创建时间 |
| updatetime | int | 10 | YES | | 更新时间 |
| token | varchar | 59 | NO | | 登录令牌 |
| status | enum | | NO | normal | 状态：normal正常，hidden禁用 |
| admin_type | tinyint | 1 | NO | 1 | 管理员类型：1超管，2OEM，3代理 |
| url | varchar | 255 | YES | | 绑定域名 |
| agent_qrcode | varchar | 255 | YES | | 代理二维码 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE KEY username (username)
- KEY admin_type (admin_type)

### 2. 用户表 (fa_users)
**表说明**：小程序用户表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| openid | varchar | 100 | NO | | 微信OpenID |
| unionid | varchar | 100 | YES | | 微信UnionID |
| nickname | varchar | 50 | YES | | 用户昵称 |
| image | varchar | 255 | YES | | 用户头像 |
| gender | tinyint | 1 | NO | 1 | 性别：1男，2女，3未知 |
| mobile | varchar | 11 | YES | | 手机号 |
| score | int | 10 | NO | 0 | 积分/算力 |
| is_vip | tinyint | 1 | NO | 2 | 是否VIP：1是，2否 |
| viptime | int | 10 | NO | 0 | VIP到期时间 |
| parent_id | int | 11 | NO | 0 | 推荐人ID |
| qrcode | varchar | 255 | YES | | 用户专属二维码 |
| logintime | int | 10 | YES | | 最后登录时间 |
| createtime | int | 10 | YES | | 创建时间 |
| status | tinyint | 1 | NO | 1 | 状态：1正常，2禁用 |
| oem_id | int | 11 | NO | 0 | 所属OEM |
| agent_id | int | 11 | NO | 0 | 所属代理 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE KEY openid (openid)
- KEY oem_id (oem_id)
- KEY agent_id (agent_id)
- KEY parent_id (parent_id)

**关联关系**：
- belongsTo Admin (oem_id -> admin.id)
- belongsTo Admin (agent_id -> admin.id)

### 3. 工具表 (fa_tools)
**表说明**：AI工具配置表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| name | varchar | 100 | NO | | 工具名称 |
| icon | varchar | 255 | YES | | 工具图标 |
| desc | text | | YES | | 工具描述 |
| rule | varchar | 50 | NO | | 工具规则标识 |
| price | decimal | 10,2 | NO | 0.00 | 单次使用价格 |
| type_data | tinyint | 1 | NO | 1 | 工具类型：1-4 |
| show_data | tinyint | 1 | NO | 1 | 显示位置：1首页，4顶部 |
| status | tinyint | 1 | NO | 1 | 状态：1启用，2禁用 |
| weigh | int | 10 | NO | 0 | 排序权重 |
| admin_id | int | 11 | NO | 0 | 所属管理员 |
| createtime | int | 10 | YES | | 创建时间 |
| updatetime | int | 10 | YES | | 更新时间 |

**索引**：
- PRIMARY KEY (id)
- KEY admin_id (admin_id)
- KEY status (status)
- KEY rule (rule)

### 4. 工具订单表 (fa_toolsorder)
**表说明**：工具使用订单表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| name | varchar | 100 | NO | | 订单名称 |
| users_id | int | 11 | NO | 0 | 用户ID |
| tools_id | int | 11 | NO | 0 | 工具ID |
| paydata | tinyint | 1 | NO | 1 | 支付方式：1积分，2微信支付 |
| price | decimal | 10,2 | NO | 0.00 | 订单金额 |
| content | text | | YES | | 订单内容（JSON格式） |
| result | text | | YES | | 处理结果 |
| success | text | | YES | | 成功结果 |
| status | tinyint | 1 | NO | 1 | 状态：1处理中，2成功，3失败 |
| endtime | int | 10 | YES | | 完成时间 |
| createtime | int | 10 | YES | | 创建时间 |
| oem_id | int | 11 | NO | 0 | 所属OEM |
| agent_id | int | 11 | NO | 0 | 所属代理 |
| tools_rule | varchar | 50 | YES | | 工具规则 |

**索引**：
- PRIMARY KEY (id)
- KEY users_id (users_id)
- KEY tools_id (tools_id)
- KEY oem_id (oem_id)
- KEY status (status)

**关联关系**：
- belongsTo Users (users_id -> users.id)
- belongsTo Tools (tools_id -> tools.id)
- belongsTo Admin (oem_id -> admin.id)
- belongsTo Admin (agent_id -> admin.id)

### 5. VIP套餐表 (fa_vip)
**表说明**：VIP会员套餐配置表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| name | varchar | 100 | NO | | 套餐名称 |
| desc | text | | YES | | 套餐描述 |
| price | decimal | 10,2 | NO | 0.00 | 套餐价格 |
| days | int | 10 | NO | 0 | 有效天数 |
| status | tinyint | 1 | NO | 1 | 状态：1启用，2禁用 |
| weigh | int | 10 | NO | 0 | 排序权重 |
| admin_id | int | 11 | NO | 0 | 所属管理员 |
| createtime | int | 10 | YES | | 创建时间 |

**索引**：
- PRIMARY KEY (id)
- KEY admin_id (admin_id)
- KEY status (status)

**关联关系**：
- belongsTo Admin (admin_id -> admin.id)

### 6. VIP订单表 (fa_viporder)
**表说明**：VIP购买订单表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| order_no | varchar | 50 | NO | | 订单号 |
| users_id | int | 11 | NO | 0 | 用户ID |
| vip_id | int | 11 | NO | 0 | VIP套餐ID |
| price | decimal | 10,2 | NO | 0.00 | 订单金额 |
| days | int | 10 | NO | 0 | 购买天数 |
| status | tinyint | 1 | NO | 0 | 状态：0待支付，1已支付，2已取消 |
| paytime | int | 10 | YES | | 支付时间 |
| endtime | int | 10 | YES | | 到期时间 |
| createtime | int | 10 | YES | | 创建时间 |
| oem_id | int | 11 | NO | 0 | 所属OEM |
| agent_id | int | 11 | NO | 0 | 所属代理 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE KEY order_no (order_no)
- KEY users_id (users_id)
- KEY vip_id (vip_id)
- KEY status (status)

**关联关系**：
- belongsTo Users (users_id -> users.id)
- belongsTo Vip (vip_id -> vip.id)
- belongsTo Admin (oem_id -> admin.id)
- belongsTo Admin (agent_id -> admin.id)

### 7. 工具次数表 (fa_toolsnum)
**表说明**：用户工具使用次数记录表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| users_id | int | 11 | NO | 0 | 用户ID |
| tools_id | int | 11 | NO | 0 | 工具ID |
| vip_id | int | 11 | NO | 0 | VIP套餐ID |
| num | int | 10 | NO | 0 | 剩余次数 |
| admin_id | int | 11 | NO | 0 | 所属管理员 |
| createtime | int | 10 | YES | | 创建时间 |

**索引**：
- PRIMARY KEY (id)
- KEY users_id (users_id)
- KEY tools_id (tools_id)
- KEY admin_id (admin_id)

**关联关系**：
- belongsTo Users (users_id -> users.id)
- belongsTo Tools (tools_id -> tools.id)
- belongsTo Vip (vip_id -> vip.id)
- belongsTo Admin (admin_id -> admin.id)

### 8. OEM配置表 (fa_oemconfig)
**表说明**：多租户配置表

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | NO | | 主键ID |
| name | varchar | 100 | NO | | 配置名称 |
| group | varchar | 50 | NO | | 配置分组 |
| title | varchar | 100 | YES | | 配置标题 |
| tip | varchar | 255 | YES | | 配置提示 |
| type | varchar | 30 | NO | string | 配置类型 |
| value | text | | YES | | 配置值 |
| content | text | | YES | | 配置内容 |
| rule | varchar | 100 | YES | | 验证规则 |
| extend | varchar | 255 | YES | | 扩展属性 |
| admin_id | int | 11 | NO | 0 | 所属管理员 |

**索引**：
- PRIMARY KEY (id)
- KEY admin_id (admin_id)
- KEY name (name)
- KEY group (group)

**配置分组说明**：
- `xcx`：小程序配置（appid、secret、名称等）
- `wxpay`：微信支付配置
- `tencent`：腾讯云服务配置
- `aliyun`：阿里云服务配置
- `system`：系统配置

## 辅助数据表

### 9. 轮播图表 (fa_banner)
**表说明**：首页轮播图配置

### 10. 页面表 (fa_pages)
**表说明**：页面内容管理

### 11. 人物表 (fa_human)
**表说明**：AI写真人物模型

### 12. 绘画表 (fa_draw)
**表说明**：AI绘画记录

### 13. 佣金表 (fa_commissions)
**表说明**：推广佣金记录

### 14. 日志表 (fa_sllog)
**表说明**：系统操作日志

## 数据库关系图

```
fa_admin (管理员)
├── fa_users (用户) [oem_id, agent_id]
├── fa_tools (工具) [admin_id]
├── fa_vip (VIP套餐) [admin_id]
├── fa_oemconfig (配置) [admin_id]
└── fa_toolsnum (工具次数) [admin_id]

fa_users (用户)
├── fa_toolsorder (工具订单) [users_id]
├── fa_viporder (VIP订单) [users_id]
└── fa_toolsnum (工具次数) [users_id]

fa_tools (工具)
├── fa_toolsorder (工具订单) [tools_id]
└── fa_toolsnum (工具次数) [tools_id]

fa_vip (VIP套餐)
├── fa_viporder (VIP订单) [vip_id]
└── fa_toolsnum (工具次数) [vip_id]
```

## 数据库优化建议

### 1. 索引优化
- 为常用查询字段添加索引
- 复合索引优化多条件查询
- 定期分析慢查询日志

### 2. 分区策略
- 按时间分区大表（如订单表）
- 按租户ID分区多租户数据

### 3. 数据归档
- 定期归档历史订单数据
- 清理过期的临时数据

### 4. 备份策略
- 每日全量备份
- 实时增量备份
- 异地备份存储

---

**更新日期**：2025年1月  
**文档版本**：v1.0
