<?php

namespace app\admin\command;

use app\admin\model\AuthRule;
use app\common\model\Toolsorder;
use app\service\Utils;
use ReflectionClass;
use ReflectionMethod;
use think\Cache;
use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Loader;

class Audio extends Command
{
    protected $model = null;

    protected function configure()
    {
        $this
            ->setName('audio')
            ->addArgument('param1', Argument::REQUIRED, '这是第一个必需参数')
            ->setDescription('Build auth menu from controller');
        //要执行的controller必须一样，不适用模糊查询
    }

    protected function execute(Input $input, Output $output)
    {
        $admin_id = $input->getArgument('param1');
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_yinse',
            'success' => array('neq',''),
            'oem_id' => $admin_id
        ];
        $tools_order_list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        // dump($tools_order_list);
        foreach ($tools_order_list as $k=>$v){

            $tools_order_model = new Toolsorder();
            $success = json_decode($v['success'],true);
            if($success['TaskId'])
            {
                $dirPath = './uploads/xcx/'.$v['oem_id'].'/audio/';
                if (!is_dir($dirPath)) {
                    mkdir($dirPath, 0777, true); // 第三个参数为 true 表示递归创建父目录
                }
                $params = [
                    'TaskId' => $success['TaskId'],
                ];
                $utils = new Utils();
                $result = $utils->DescribeVRSTaskStatus($params,$v['oem_id']);
//                dump($result);
                if($result['code'] == 1){
                    $arr = json_decode($result['data'],true);
                    if($arr['Data']['TaskId'] && $arr['Data']['Status'] == 2){

                        $res = $utils->createAudio(['text' => '您好，这里是AI智能数字人','FastVoiceType' => $arr['Data']['FastVoiceType']],$v['oem_id']);
                        if($res['code'] == 1){
                            $success = [
                                'text' => "FastVoiceType=".$arr['Data']['FastVoiceType'],
                                'audio' => $res['data']['url']
                            ];
                            $upd_data = [
                                'success' => json_encode($success),
                                'status' => 2,
                                'endtime' => time(),
                            ];

                            $tools_order_model->save($upd_data,['id'=>$v['id']]);
                        }else
                        {
                            $error = [
                                'text' => "音色克隆成功FastVoiceType=".$arr['Data']['FastVoiceType'].';但是生成测试音频失败'.json_encode($res),
                            ];
                            $upd_data = [
                                'error' => json_encode($error),
                                'status' => 3,
                                'endtime' => time(),
                            ];

                            $tools_order_model->save($upd_data,['id'=>$v['id']]);
                        }
                    }elseif (in_array($arr['Data']['Status'],[0,1]))
                    {
                        continue;
                    }else
                    {
                        $error = [
                            'text' => '声音复刻失败'.$result['data']
                        ];
                        $upd_data = [
                            'error' => json_encode($error),
                            'status' => 3,
                            'endtime' => time(),
                        ];
                        $tools_order_model->save($upd_data,['id'=>$v['id']]);
                    }
                }else
                {
                    $error = [
                        'text' => $result['msg'],
                    ];
                    $upd_data = [
                        'error' => json_encode($error),
                        'status' => 3,
                        'endtime' => time(),
                    ];
                    $tools_order_model->save($upd_data,['id'=>$v['id']]);
                }
            }else
            {
                $output->info($v['id'].'不存在任务ID信息');
                continue;
            }

        }
    }


}
