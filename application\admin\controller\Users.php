<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\service\Inspect;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 用户管理
 *
 * @icon fa fa-circle-o
 */
class Users extends Backend
{

    /**
     * Users模型对象
     * @var \app\common\model\Users
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Users;
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("isVipList", $this->model->getIsVipList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where_pid = [];
            if($this->auth->admin_type == 2)
            {
                $where_pid['oem_id'] = $this->auth->id;
            }elseif ($this->auth->admin_type == 3)
            {
                $where_pid['agent_id'] = $this->auth->id;
            }
            $list = $this->model
                    ->with(['oem','agent'])
                    ->where($where)
                    ->where($where_pid)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('oem')->visible(['nickname']);
                $row->getRelation('agent')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }




    //修改用户算力
    public function score($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if($this->auth->id != 1)
        {
            if($this->auth->admin_type == 2)
            {
                $usersIds = $this->getUsersIds('oem',$this->auth->id);
            }else
            {
                $usersIds = $this->getUsersIds('agent',$this->auth->id);
            }
            if (is_array($usersIds) && !in_array($ids, $usersIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        if($params['num'] < 0 || !isset($params['num']))
        {
            $this->error(__('请正确输入变动量'));
        }
        if($params['type'] == 1)
        {
            $params['score'] = $row['score'] + $params['num'];
        }else
        {
            $params['score'] = $row['score'] - $params['num'];
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    //修改用户工具次数
    public function tools($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $toolsnum_model = new \app\common\model\Toolsnum();
        $agent_num = [];
        if($this->auth->id != 1)
        {
            if($this->auth->admin_type == 2)
            {
                $usersIds = $this->getUsersIds('oem',$this->auth->id);
            }else
            {
                $agent_num = $toolsnum_model->where('admin_id',$this->auth->id)->select();
                $usersIds = $this->getUsersIds('agent',$this->auth->id);
            }
            if (is_array($usersIds) && !in_array($ids, $usersIds)) {
                $this->error(__('You have no permission'));
            }
        }
        //获取当前用户所属贴牌的所有工具
        $oem_id = $row['oem_id'];
        $tools_model = new \app\common\model\Tools();
        $tools_list = $tools_model->where('admin_id',$oem_id)->select();
        //获取用户所有工具的次数
        $user_num = $toolsnum_model->where('users_id',$ids)->select();
        if (false === $this->request->isPost()) {
//            dump($tools_list);
//            die();
            $this->view->assign('tools_list', $tools_list);
            $this->view->assign('user_num', $user_num);
            $this->view->assign('agent_num', $agent_num);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $result = false;
        Db::startTrans();
        try {
            //循环参数，判断是否为代理、 代理次数是否足够

            $inspect = new Inspect();
            foreach ($params as $k=>$v)
            {
                //只有新增才去判断代理次数是否足够
                if($params['type'] == 1)
                {
                    //
                    if($k != 'type' && !empty($v))
                    {

                        $tools_num_id_arr = explode('_',$k);
                        $tools_name = $tools_model->where('id',$tools_num_id_arr[1])->value('name');
                        if($this->auth->admin_type == 3 )
                        {
                            $agent_tools_num = $toolsnum_model->where('tools_id',$tools_num_id_arr[1])->where('admin_id',$this->auth->id)->value('num');
                            if($agent_tools_num < $v)
                            {
                                exception('您的'.$tools_name.'次数不足，不能增加');
                            }
                        }
//                        dump($tools_num_id_arr[0]);
//                        die();
//                        $toolsnum_model->where('id',$tools_num_id_arr[0])->where('users_id',$row['id'])->setInc('num',$v);
                        $inspect->updateUsersScoreOrNum('num',$row['id'],$tools_num_id_arr[1],1,'后台增加'.$tools_name,$v);
                    }

                }else
                {
                    if($k != 'type' && !empty($v))
                    {
                        $tools_num_id_arr = explode('_',$k);
                        
                        $tools_name = $tools_model->where('id',$tools_num_id_arr[1])->value('name');
//                        $toolsnum_model->where('id',$tools_num_id_arr[0])->where('users_id',$row['id'])->setDec('num',$v);

                        $inspect->updateUsersScoreOrNum('num',$row['id'],$tools_num_id_arr[1],2,'后台减少'.$tools_name,$v);
                    }
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
//        if (false === $result) {
//            $this->error(__('No rows were updated'));
//        }
        $this->success('修改成功');
    }


    public function status($status = 1, $ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if($this->auth->id != 1)
        {
            if($this->auth->admin_type == 2)
            {
                $usersIds = $this->getUsersIds('oem',$this->auth->id);
            }else
            {
                $usersIds = $this->getUsersIds('agent',$this->auth->id);
            }
            if (is_array($usersIds) && !in_array($ids, $usersIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isAjax()) {
            $ret = $this->model->where('id', $ids)->update(['status' => $status]);
            if ($ret) {
                $this->success('修改成功');
            }else
            {
                $this->error('修改失败');
            }

        }
        $this->error('错误');
    }

    public function getUsersIds($admin_type = 'oem',$pid = null)
    {
        if($pid)
        {
            $where = [];
            if($admin_type == 'oem')
            {
                $where['oem_id'] = $pid;
            }else
            {
                $where['agent_id'] = $pid;
            }
            return $this->model->where($where)->column('id');
        }else
        {
            return [];
        }
    }

}
