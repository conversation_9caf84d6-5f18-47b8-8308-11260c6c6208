<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\library\WxPay;
use app\service\WxTransferService;
use think\Db;

/**
 * 提现管理
 *
 * @icon fa fa-circle-o
 */
class Draw extends Backend
{

    /**
     * Draw模型对象
     * @var \app\common\model\Draw
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Draw;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where_pid = [];
            if($this->auth->admin_type == 2)
            {
                $where_pid['draw.oem_id'] = $this->auth->id;
            }elseif ($this->auth->admin_type == 3)
            {
                $where_pid['draw.agent_id'] = $this->auth->id;
            }
            $list = $this->model
                    ->with(['oem','agent','users'])
                    ->where($where)
                    ->where($where_pid)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('oem')->visible(['nickname']);
                $row->getRelation('agent')->visible(['nickname']);
				$row->getRelation('users')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    
    public function reject($ids)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        //驳回
        // $wxPay = new WxPay($this->auth->id);
        // $result = $wxPay->refund($row['wx_order_number'],$row['price'],$row['price']);
        Db::startTrans();
        try {
            $this->model->where('id', $ids)->update(['status' => 3]);
            $users_model = new \app\common\model\Users();
            $users_model->where('id', $row['users_id'])->setInc('money', $row['money']);
            Db::commit();
        }catch (\Exception $e) {
            Db::rollback();
            $this->error('驳回失败');
        }
        $this->success('驳回成功');
    }
    
    public function withdrawal($ids)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $transfer = new WxTransferService($this->auth->id);
        $users_model = new \app\common\model\Users();
        $users_info = $users_model->where('id', $row['users_id'])->find();
        $result = $transfer->crteateMchPay(['order_sn' => $row['number']],$users_info['openid'],$row['commission_actual']);
        // dump($result);
        if($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS')
        {
            $this->model->where('id', $ids)->update(['status' => 4,'wx_number' => $result['transfer_bill_no'],'mark' => $result['package_info']]);
            $this->success('操作成功');
        }else
        {
            $this->error('操作失败');
        }
        //驳回
        // $wxPay = new WxPay($this->auth->id);
        // $users_model = new \app\common\model\Users();
        // $users_info = $users_model->where('id', $row['users_id'])->find();
        // $result = $wxPay->toBalance($row['number'],$users_info['openid'],$row['commission_actual']);
        // dump($result);
//        // $result = $wxPay->refund($row['wx_order_number'],$row['price'],$row['price']);
//        Db::startTrans();
//        try {
//            $this->model->where('id', $ids)->update(['status' => 3]);
//            $users_model = new \app\common\model\Users();
//            $users_model->where('id', $row['users_id'])->setInc('money', $row['money']);
//            Db::commit();
//        }catch (\Exception $e) {
//            Db::rollback();
//            $this->error('驳回失败');
//        }
//        $this->success('驳回成功');

    }

}
