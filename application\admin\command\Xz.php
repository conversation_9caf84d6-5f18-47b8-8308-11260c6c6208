<?php

namespace app\admin\command;

use app\admin\model\AuthRule;
use app\common\model\Toolsorder;
use app\service\Utils;
use ReflectionClass;
use ReflectionMethod;
use think\Cache;
use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Loader;

class Xz extends Command
{
    protected $model = null;

    protected function configure()
    {
        $this
            ->setName('xz')
            ->addArgument('param1', Argument::REQUIRED, '这是第一个必需参数')
            ->setDescription('Build auth menu from controller');
        //要执行的controller必须一样，不适用模糊查询
    }

    protected function execute(Input $input, Output $output)
    {
        $admin_id = $input->getArgument('param1');
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_xiezhen',
            'oem_id' => $admin_id
        ];
          

        $list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        $utils = new Utils();
        foreach($list as $k=>$v){
            $tools_order_model = new Toolsorder();
            $success = json_decode($v['success'],true);
            
            $output->info($v['success']);
//            dump($success['JobId']);
            $params = [
                'JobId' => $success['JobId'],
            ];
            $dirPath = '/uploads/'.date('Ymd',time()).'/';
            if (!is_dir('.'.$dirPath)) {
                mkdir('.'.$dirPath, 0777, true); // 第三个参数为 true 表示递归创建父目录
            }
            $result = $utils->getStatusPortraitJob($params,$v['oem_id']);
            file_put_contents('tiaowu.txt',json_encode($result));
            if($result['code'] == 1){
                $data = json_decode($result['data'],true);
//                dump($data);
                if($data['JobStatusCode'] == 'DONE'){
                    //完成了
                    $imageUrl = $data['ResultUrls'][0];
                    //把图片上传到oss
                    // 本地临时文件名，用于保存从网址下载的文件
                    $localTempFile = $dirPath.time().'_'.$v['users_id'].'.png';
                    // 下载网址文件到本地临时文件
                    file_put_contents('.'.$localTempFile, file_get_contents($imageUrl));
                    $oss_result = $utils->uploadOss($localTempFile,$v['oem_id']);
                    if($oss_result['code'] == 1) {

                        $success['image'] = $oss_result['data']['url'];
                        $upd_data['success'] = json_encode($success);
                        $upd_data['status'] = 2;
                        $upd_data['endtime'] = time();
                    }else
                    {
                        //上传oss失败
                        //失败了
                        $success['text'] = '上传oss失败'.$oss_result['msg'];
                        $upd_data['error'] = json_encode($success);;
                        $upd_data['status'] = 3;
                        $upd_data['endtime'] = time();
                    }
                }elseif ($data['JobStatusCode'] == 'FAIL'){
                    //失败了
                    $success['text'] = $result['data'];
                    $upd_data['error'] = json_encode($success);;
                    $upd_data['status'] = 3;
                    $upd_data['endtime'] = time();
                }else
                {
                    continue;
                }
            }else
            {
                $success['text'] = $result['msg'];
                $upd_data['error'] = json_encode($success);;
                $upd_data['status'] = 3;
                $upd_data['endtime'] = time();
            }
            $tools_order_model->save($upd_data,['id'=>$v['id']]);
        }
    }


}
