<?php

namespace app\service;

use app\common\model\Oemconfig;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;

class WxTransferService
{
    // 微信支付配置
    protected $appid;
    protected $mchid;
    protected $mch_serial_number;
    protected $private_key;
    protected $certificate;
    protected $notifyUrl;
    protected $instance;
    protected $oem_id;
    public function __construct($oem_id)
    {
        $this->oem_id = $oem_id;
        $oem_config_model = new Oemconfig();
        $payConfig = $oem_config_model->where('group','in','wxpay,xcx,system')->where('admin_id',$oem_id)->select();
        if(!empty($payConfig)){
            foreach ($payConfig as $pay){
                $this->config[$pay['name']] = $pay['value'];
            }
        }
        $this->appid = $this->config['xcx_appid'];
        $this->mchid = $this->config['wx_mchid'];
        // $this->mch_serial_number = $this->config['mch_serial_number'];
        $this->private_key = '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_key.pem';
        $this->certificate = '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_cert.pem';
        $this->notifyUrl = $this->config['sys_url'].'/api/wxpay/wx_notify1';

        // 初始化微信支付实例
//        $this->initWxPayInstance();
    }

    /**
     * @param $payment
     * @param $money
     * @param $openid
     * @功能说明:新版商家付款到零钱 2025.1.18更新
     */
    public function crteateMchPay($payment,$openid,$money,$user_name=''){

        $url = 'https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills';//新版地址

        $pars = [];


        $order_code = 'WD'.time().mt_rand(10000, 99999);

        $pars['appid'] = $this->appid;//直连商户的appid

        $pars['out_bill_no'] = $payment['order_sn'];//【商户单号】 商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一

        $pars['transfer_scene_id'] = '1000';//【转账场景ID】 该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1001-现金营销

        $pars['openid'] = $openid;//【收款用户OpenID】 商户AppID下，某用户的OpenID

        $pars['transfer_amount'] = (int)($money * 100);//【转账金额】 转账金额单位为“分”。

        $Wechatpay = 0;
        //大于等于2000必须要姓名
        if($pars['transfer_amount']>=200000 &&!empty($user_name)){

            $pars['user_name'] = $this->getEncrypt($user_name,$payment);//【收款用户姓名】 收款方真实姓名。需要加密传入，支持标准RSA算法和国密算法，公钥由微信侧提供。转账金额 >= 2,000元时，该笔明细必须填写若商户传入收款用户姓名，微信支付会校验收款用户与输入姓名是否一致，并提供电子回单

            $Wechatpay = 1;
        }

        $pars['transfer_remark'] = '商家转账';//【转账备注】 转账备注，用户收款时可见该备注信息，UTF8编码，最多允许32个字符


        $pars['transfer_scene_report_infos'] =
            [
                [

                    'info_type'  => '活动名称',

                    'info_content'=> '申请提现',

                ],
                [

                    'info_type'  => '奖励说明',

                    'info_content'=> '提现到账',

                ]
            ];//转账明细列表


        $token  = $this->getToken($pars,$payment);
        // dump(json_encode($pars));
        $res    = $this->http_post($url,json_encode($pars),$token,$payment,$Wechatpay);//发送请求

        $resArr = json_decode($res,true);

        if(!empty($resArr['code'])){

            $resArr['result_code'] = $resArr['return_code'] = 'fail';

            $resArr['err_code_des'] = $resArr['message'];

        }else{

            $resArr['result_code'] = $resArr['return_code'] = 'SUCCESS';
        }

        return $resArr;
        //成功返回
    }

    /**
     * @param $str
     * @param $payment
     * @功能说明:根据平台证书加密
     */
    public function getEncrypt($str,$payment) {
        //$str是待加密字符串
        $public_key_path = $payment['payment']['wx_certificates'];

        if(empty($public_key_path)){

            return '';
        }

        $public_key = file_get_contents($public_key_path);

        $encrypted = '';

        if (openssl_public_encrypt($str, $encrypted, $public_key, OPENSSL_PKCS1_OAEP_PADDING)) {
            //base64编码
            $sign = base64_encode($encrypted);
        } else {
            throw new Exception('encrypt failed');
        }
        return $sign;
    }


    /**
     * @功能说明:查询转账记录
     */
    public function getMchPayRecord($order_code,$payment){

        $url = 'https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/'.$order_code;
        //请求方式
        $token  = $this->getToken([],$payment,'GET',$url);

        $res    = $this->https_request($url,[],$token);//发送请求

        $resArr = json_decode($res,true);

        return $resArr;
    }

    public function https_request($url,$data,$token)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, (string)$url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //添加请求头
        $headers = [
            'Authorization:WECHATPAY2-SHA256-RSA2048 ' . $token,
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
        ];
        if (!empty($headers)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }


    /**
     * @param $payment
     * @功能说明:获取证书序列号
     */
    public function get_Certificates($payment)
    {

        $platformCertificateFilePath = $payment['payment']['wx_certificates'];

        if(empty($platformCertificateFilePath)){

            return '';
        }

        $a = openssl_x509_parse(file_get_contents($platformCertificateFilePath));

        return !empty($a['serialNumberHex'])?$a['serialNumberHex']:'';
    }


    /**
     * POST 请求
     */
    private function http_post($url,$data,$token,$payment,$Wechatpay=0){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, (string)$url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)){
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //添加请求头
        $headers = [

            'Authorization:WECHATPAY2-SHA256-RSA2048 '.$token,
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
        ];
        //需要加密
        if($Wechatpay==1){

            $certificates = $this->get_Certificates($payment);

            $headers[] = 'Wechatpay-Serial:'.$certificates;
        }
        if(!empty($headers)){

            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        $output = curl_exec($curl);
        curl_close($curl);

        return $output;
    }


    /**
     * @param $pars
     * @param $payment
     * @功能说明:获取token
     */
    public function getToken($pars,$payment,$http_method='POST',$usrl_data='')
    {

        $url = !empty($usrl_data)?$usrl_data:'https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills';
        // $http_method = 'POST';//请求方法（GET,POST,PUT）
        $timestamp = time();//请求时间戳
        $url_parts = parse_url($url);//获取请求的绝对URL
        $nonce = $timestamp . rand(10000, 99999);//请求随机串
        $body = !empty($pars)?json_encode((object)$pars):'';//请求报文主体
        $stream_opts = [
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
            ]
        ];
        $apiclient_cert_path = $this->certificate;
        $apiclient_key_path  = $this->private_key;
        $apiclient_cert_arr = openssl_x509_parse(file_get_contents($apiclient_cert_path, false, stream_context_create($stream_opts)));
        $serial_no = $apiclient_cert_arr['serialNumberHex'];//证书序列号
        $mch_private_key = file_get_contents($apiclient_key_path, false, stream_context_create($stream_opts));//密钥
        $merchant_id = $this->mchid;//商户id
        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
        $message = $http_method . "\n" .
            $canonical_url . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            $body . "\n";
        openssl_sign($message, $raw_sign, $mch_private_key, 'sha256WithRSAEncryption');

        $sign = base64_encode($raw_sign);//签名
        $schema = 'WECHATPAY2-SHA256-RSA2048';
        $token = sprintf('mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
            $merchant_id, $nonce, $timestamp, $serial_no, $sign);//微信返回token
        return $token;

    }


}