define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template'], function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template) {

    var Controller = {
        index: function () {
            // 基于准备好的dom，初始化echarts实例
            var myChart = Echarts.init(document.getElementById('echart'), 'walden');
            var myChart1 = Echarts.init(document.getElementById('echart1'), 'walden');

            // 基于准备好的dom，初始化echarts实例
            var myChart2 = Echarts.init(document.getElementById('echart2'), 'walden');

            // 指定图表的配置项和数据
            var option = {
                title: {
                    text: '',
                    subtext: ''
                },
                color: [
                    "#18d1b1",
                    "#3fb1e3",
                    "#626c91",
                    "#a0a7e6",
                    "#c4ebad",
                    "#96dee8"
                ],
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['订单金额','退款金额']
                },
                toolbox: {
                    show: false,
                    feature: {
                        magicType: {show: true, type: ['stack', 'tiled']},
                        saveAsImage: {show: true}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: Config.column
                },
                yAxis: {},
                grid: [{
                    left: 'left',
                    top: 'top',
                    right: '10',
                    bottom: 30
                }],
                series: [{
                    name: __('订单金额'),
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        normal: {}
                    },
                    lineStyle: {
                        normal: {
                            width: 1.5
                        }
                    },
                    data: Config.paydata
                },{
                    name: __('退款金额'),
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        normal: {}
                    },
                    lineStyle: {
                        normal: {
                            width: 1.5
                        }
                    },
                    data: Config.refunddata
                }]
            };

            // 指定图表的配置项和数据
            var option1 = {
                title: {
                    text: '',
                    subtext: ''
                },
                color: [
                    "#18d1b1",
                    "#3fb1e3",
                    "#626c91",
                    "#a0a7e6",
                    "#c4ebad",
                    "#96dee8"
                ],
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['工具使用次数']
                },
                toolbox: {
                    show: false,
                    feature: {
                        magicType: {show: true, type: ['stack', 'tiled']},
                        saveAsImage: {show: true}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: Config.column
                },
                yAxis: {},
                grid: [{
                    left: 'left',
                    top: 'top',
                    right: '10',
                    bottom: 30
                }],
                series: [{
                    name: __('工具使用次数'),
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        normal: {}
                    },
                    lineStyle: {
                        normal: {
                            width: 1.5
                        }
                    },
                    data: Config.toolsorderdata
                }]
            };




// 指定图表的配置项和数据
            var option2 = {
                title: {
                    text: '各项工具使用分析',
                    subtext: Config.time_text,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                },
                series: [
                    {
                        name: '各项工具使用分析',
                        type: 'pie',
                        radius: '50%',
                        data: Config.toolsnumdata,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };

// 使用刚指定的配置项和数据显示图表。

            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
            myChart1.setOption(option1);
            myChart2.setOption(option2);

            $(window).resize(function () {
                myChart.resize();
                myChart1.resize();
                myChart2.resize();
            });

            $(document).on("click", ".btn-refresh", function () {
                setTimeout(function () {
                    myChart.resize();
                    myChart1.resize();
                    myChart2.resize();
                }, 0);
            });
            $(document).on("click", ".btn-filter", function () {
                $.ajax({
                    url: 'dashboard/tools?type='+$(this).attr('data-charts'), // 控制器和方法地址
                    type: 'get', // 请求方式
                    dataType: 'json', // 预期服务器返回的数据类型

                    success: function(response) {
                        // console.log(response);
                        var new_option = {
                            title: {
                                subtext: response.time_text,
                            },
                            series: [
                                {

                                    data: response.tools_num,

                                }
                            ]
                        };;
                        myChart2.setOption(new_option);
                        // 请求成功时的回调函数
                        console.log(response);
                    },
                    error: function(xhr, status, error) {
                        // 请求失败时的回调函数
                        console.error("An error occurred: " + error);
                    }
                });
            });


        }
    };


    return Controller;
});
