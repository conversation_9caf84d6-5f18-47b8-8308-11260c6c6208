<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Show_data')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="showDataList" item="vo"}
                <label for="row[show_data]-{$key}"><input id="row[show_data]-{$key}" name="row[show_data]" type="radio" value="{$key}" {in name="key" value="$row.show_data"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_data')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="typeDataList" item="vo"}
                <label for="row[type_data]-{$key}"><input id="row[type_data]-{$key}" name="row[type_data]" type="radio" value="{$key}" {in name="key" value="$row.type_data"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" id="pages_id">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pages_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pages_id" data-rule="required" data-source="pages/index" class="form-control selectpage" name="row[pages_id]" type="text" value="{$row.pages_id|htmlentities}">
        </div>
    </div>
    <div class="form-group" id="url" style="display: none">
        <label class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-url" class="form-control" name="row[url]" type="text" value="{$row.url|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
