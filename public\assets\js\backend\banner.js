define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'banner/index' + location.search,
                    add_url: 'banner/add',
                    edit_url: 'banner/edit',
                    del_url: 'banner/del',
                    multi_url: 'banner/multi',
                    import_url: 'banner/import',
                    table: 'banner',
                    dragsort_url:'',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'pages.name', title: __('Pages_id')},
                        {field: 'url', title: __('Url'), operate: 'LIKE', formatter: Table.api.formatter.url},
                        {field: 'type_data', title: __('Type_data'), searchList: {"1":__('Type_data 1'),"2":__('Type_data 2')}, formatter: Table.api.formatter.normal},
                        {field: 'show_data', title: __('Show_data'), searchList: {"1":__('Show_data 1'),"2":__('Show_data 2'),"3":__('Show_data 3'),"4":__('Show_data 4')}, formatter: Table.api.formatter.normal},
                        {field: 'weigh', title: __('Weigh'), operate: false},
                        {field: 'admin.nickname', title: __('Admin_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show},
                        // {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'pages.name', title: __('Pages.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            $('input[name="row[type_data]"]').change(function() {
                console.log('选中的值:', $(this).val());
                // 在这里执行你的逻辑代码
                if ($(this).val() == '1') {
                    $("#pages_id").css("display", "block");
                    $("#url").css("display", "none");
                }else
                {
                    $("#pages_id").css("display", "none");
                    $("#url").css("display", "block");
                }
            });
            Controller.api.bindevent();
        },
        edit: function () {
            $('input[name="row[type_data]"]').change(function() {
                console.log('选中的值:', $(this).val());
                // 在这里执行你的逻辑代码
                if ($(this).val() == '1') {
                    $("#pages_id").css("display", "block");
                    $("#url").css("display", "none");
                }else
                {
                    $("#pages_id").css("display", "none");
                    $("#url").css("display", "block");
                }
            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
