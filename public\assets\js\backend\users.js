define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            if(Config.show) {
                var delurl = 'users/del';
            }else
            {
                var delurl = '';
            }
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'users/index' + location.search,
                    add_url: 'users/add',
                    edit_url: 'users/edit',
                    del_url: delurl,
                    multi_url: 'users/multi',
                    import_url: 'users/import',
                    table: 'users',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'openid', title: __('Openid'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'gender', title: __('Gender'), searchList: {"1":__('Gender 1'),"2":__('Gender 2'),"3":__('Gender 3')}, formatter: Table.api.formatter.normal},
                        {field: 'phone', title: __('Phone'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'money', title: __('Money'),operate: false,sortable:true},
                        {field: 'score', title: __('Score'),operate: false,sortable:true},
                        {field: 'is_vip', title: __('Is_vip'), searchList: {"1":__('Is_vip 1'),"2":__('Is_vip 2')}, formatter: Table.api.formatter.normal},
                        {field: 'viptime', title: __('Viptime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'parent_id', title: __('Parent_id'),operate: 'LIKE'},
                        {field: 'ip', title: __('Ip'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'logintime', title: __('Logintime'), operate:false, addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'oem.nickname', title: __('Oem_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show},
                        {field: 'agent.nickname', title: __('Agent_id'),operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2')}, formatter: Table.api.formatter.status},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                                                {
                                    name: 'detail',
                                    title: __('账户算力'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    text: __('账户算力'),
                                    url: 'users/score',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                },
                                {
                                    name: 'detail',
                                    title: __('工具次数'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    text: __('工具次数'),
                                    url: 'users/tools',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                },
                                {
                                    name: 'ajax',
                                    title: __('禁用'),
                                    classname: 'btn btn-xs btn-danger  btn-magic btn-ajax',
                                    text:"禁用",
                                    confirm: '确认要禁用该用户？',
                                    url: 'users/status?status=2',
                                    success: function (data, ret) {
                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 1)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },
                                {
                                    name: 'ajax',
                                    title: __('启用'),
                                    classname: 'btn btn-xs btn-success btn-magic btn-ajax',
                                    text:"启用",
                                    confirm: '确认要启用该用户？',
                                    url: 'users/status?status=1',
                                    success: function (data, ret) {

                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 2)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },

                            ],formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        score: function () {
            Controller.api.bindevent();
        },
        tools: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
