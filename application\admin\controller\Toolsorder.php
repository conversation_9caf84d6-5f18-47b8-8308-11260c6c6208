<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 工具使用记录
 *
 * @icon fa fa-circle-o
 */
class Toolsorder extends Backend
{

    /**
     * Toolsorder模型对象
     * @var \app\common\model\Toolsorder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Toolsorder;
        $this->view->assign("paydataList", $this->model->getPaydataList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where_pid = [];
            if($this->auth->admin_type == 2)
            {
                $where_pid['toolsorder.oem_id'] = $this->auth->id;
            }elseif ($this->auth->admin_type == 3)
            {
                $where_pid['toolsorder.agent_id'] = $this->auth->id;
            }
            $list = $this->model
                    ->with(['users','oem','agent','tools'])
                    ->where($where)
                    ->where($where_pid)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('users')->visible(['nickname']);
				$row->getRelation('oem')->visible(['nickname']);
				$row->getRelation('agent')->visible(['nickname']);
				$row->getRelation('tools')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    //获取工具订单信息
    public function content($ids = null,$type = 1)
    {
        $row = $this->model->get($ids);
        if ($type == 1)
        {
            if($row['tools_rule'] == 'ai_chat')
            {

                $conten = ['text' => urldecode($row['content'])];
            }else
            {

                $conten = json_decode($row['content'],true);
            }
        }
        elseif ($type == 2)
        {
            if($row['tools_rule'] == 'ai_chat')
            {

                $conten = ['text' => $row['success']];
//                $conten = $row['success'];
            }else
            {

                $conten = json_decode($row['success'],true);
            }
        }elseif ($type == 3)
        {
            $conten = json_decode($row['error'],true); 
        }
        $this->view->assign('content', $conten);
        return $this->view->fetch();
    }

}
