define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'sllog/index' + location.search,
                    add_url: 'sllog/add',
                    edit_url: 'sllog/edit',
                    del_url: 'sllog/del',
                    multi_url: 'sllog/multi',
                    import_url: 'sllog/import',
                    table: 'sllog',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'users.nickname', title: __('Users_id')},
                        {field: 'tools.name', title: __('Tools_id')},
                        {field: 'price', title: __('Price'), operate: false,formatter: function (value,row,index) {
                                // 动态显示年龄列的值，这里可以根据业务需求进行修改
                                if(row.type == 2)
                                {
                                    return value + '次数';
                                }else
                                {
                                    return value + '算力';
                                }
                            }},
                        {field: 'before', title: __('Before'), operate: false},
                        {field: 'after', title: __('After'), operate: false},
                        {field: 'mark', title: __('Mark'), operate: false},
                        {field: 'type_data', title: __('Type_data'), searchList: {"1":__('Type_data 1'),"2":__('Type_data 2')}, formatter: Table.api.formatter.normal},
                        // {field: 'dec_data', title: __('Dec_data'), searchList: {"1":__('Dec_data 1'),"2":__('Dec_data 2')}, formatter: Table.api.formatter.normal},
                        {field: 'oem.nickname', title: __('Oem_id'), operate: Config.show ? 'LIKE' : Config.show,visible:Config.show,},
                        {field: 'agent.nickname', title: __('Agent_id'), operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show,},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
