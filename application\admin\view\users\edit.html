<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Openid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-openid" class="form-control" name="row[openid]" type="text" disabled value="{$row.openid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" disabled value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" disabled type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--                        -->
<!--            <select  id="c-gender" class="form-control selectpicker" name="row[gender]">-->
<!--                {foreach name="genderList" item="vo"}-->
<!--                    <option value="{$key}" {in name="key" value="$row.gender"}selected{/in}>{$vo}</option>-->
<!--                {/foreach}-->
<!--            </select>-->

<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" class="form-control" name="row[phone]" disabled type="text" value="{$row.phone|htmlentities}">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-password" class="form-control" name="row[password]" type="text" value="{$row.password|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-score" class="form-control" name="row[score]" type="number" value="{$row.score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Parent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-parent_id" data-rule="" class="form-control" name="row[parent_id]" type="text" value="{$row.parent_id|htmlentities}">
        </div>
    </div>
    {if condition="$show"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Oem_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-oem_id" data-rule="required" data-field="nickname" class="form-control" name="row[oem_id]" type="text" value="{$row.oem_id|htmlentities}">
        </div>
    </div>
    {/if}
    {if condition="$agent_show"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" class="form-control" name="row[agent_id]" type="text" value="{$row.agent_id|htmlentities}">
        </div>
    </div>
    {/if}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
