<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\library\WxPay;
use app\service\Utils;

/**
 * VIP订单
 *
 * @icon fa fa-circle-o
 */
class Viporder extends Backend
{

    /**
     * Viporder模型对象
     * @var \app\common\model\Viporder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Viporder;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where_pid = [];
            if($this->auth->admin_type == 2)
            {
                $where_pid['viporder.oem_id'] = $this->auth->id;
            }elseif ($this->auth->admin_type == 3)
            {
                $where_pid['viporder.agent_id'] = $this->auth->id;
            }
            $list = $this->model
                    ->with(['vip','users','oem','agent'])
                    ->where($where)
                    ->where($where_pid)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('vip')->visible(['name']);
				$row->getRelation('users')->visible(['nickname']);
				$row->getRelation('oem')->visible(['nickname']);
				$row->getRelation('agent')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    /**
     * 退款
     * @param $ids
     * @return void
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \think\exception\DbException
     */
    public function refund($ids)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        //退款
        $wxPay = new WxPay($this->auth->id);
        $result = $wxPay->refund($row['wx_order_number'],$row['price'],$row['price']);
        if($result['code'] != 0)
        {
            $res = $row->save(['status' => 3,'endtime' => time()]);
            if($res)
            {
                $this->success('退款成功');
            }else
            {
                $this->error('退款成功，状态修改失败。');
            }
        }else
        {
            $this->error($result['msg']);
        }
    }

    public function recharge($ids)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $users_model = new \app\common\model\Users();
        $users_info = $users_model->find($row['users_id']);
        if($users_info)
        {
            $utils = new Utils();
            $result = $utils->addUsersToolsNumScoreByVip($row['vip_id'],$row['users_id'],$this->auth->id,$ids);
            if($result['code'] != 0)
            {
                $this->success('修改成功');
            }else
            {
                $this->error($result['msg']);
            }
        }else
        {
            $this->error('用户不存在');
        }
    }

}
