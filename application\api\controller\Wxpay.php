<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Sms as Smslib;
use app\common\model\Commissions;
use app\common\model\Oemconfig;
use app\common\model\Sllog;
use app\common\model\Toolsnum;
use app\common\model\User;
use app\common\model\Users;
use app\common\model\Vip;
use app\common\model\Viporder;
use think\Db;
use think\Hook;
use think\Request;

/**
 *
 */
class Wxpay
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';


    // 微信支付回调处理方法
    public function wx_notify()
    {
        // 获取微信支付回调的 XML 数据
        $xml = file_get_contents('php://input');
        if (!$xml) {
            return $this->returnXml('FAIL', '未获取到回调数据');
        }

        // 将 XML 数据转换为数组
        $data = $this->xmlToArray($xml);
        if (!$data) {
            return $this->returnXml('FAIL', 'XML 数据转换失败');
        }
        file_put_contents('wx.txt',json_encode($data)."\r\n",FILE_APPEND);

        $vip_order_model = new Viporder();
        $vip_order_info = $vip_order_model->where('order_number', $data['out_trade_no'])->find();
        if (!$vip_order_info || $vip_order_info['status'] == 2) {
            return $this->returnXml('SUCCESS', 'OK');
        }
        $oem_config_model = new Oemconfig();
        $payConfig = $oem_config_model->where('group','in','wxpay,xcx')->where('admin_id',$vip_order_info['oem_id'])->select();
        $wx_config = [];
        if(!empty($payConfig)){
            foreach ($payConfig as $pay){
                $wx_config[$pay['name']] = $pay['value'];
            }
        }

        // 验证签名
        if (!$this->verifySign($data,$wx_config['wx_key'])) {

            file_put_contents('wx.txt','签名验证失败'.$wx_config['wx_key']."\r\n",FILE_APPEND);
            return $this->returnXml('FAIL', '签名验证失败');
        }

        // 检查支付结果
        if ($data['return_code'] == 'SUCCESS' && $data['result_code'] == 'SUCCESS') {
            // 处理业务逻辑，例如更新订单状态
            $order_sn = $data['out_trade_no'];
            try {
                // 检查订单是否已经处理过


                // 开始事务
                Db::startTrans();

                $upd_data = [
                    'status' => 2,
                    'paytime' => time(),
                    'wx_order_number' => $data['transaction_id'],
                ];
                // 更新订单状态为已支付
                $result = $vip_order_model->where('order_number', $order_sn)->update($upd_data);

                if (!$result) {
                    file_put_contents('wx.txt','更新订单状态失败'.json_encode($upd_data)."\r\n",FILE_APPEND);
                    throw new \Exception('更新订单状态失败');
                }
                $users_model = new Users();
                $user_info = $users_model->where('id', $vip_order_info['users_id'])->find();
                $vip_model = new Vip();
                $vip_info = $vip_model->where('id', $vip_order_info['vip_id'])->find();
                $viptime = $vip_info['days'] * 24 * 60 * 60;
                if($user_info['viptime'] > time())
                {
                    $viptime = $user_info['viptime'] + $viptime;
                }else
                {
                    $viptime = time() + $viptime;
                }
                $users_upd_data = [
                    'is_vip' => 1,
                    'viptime' => $viptime,
                ];
//                if($vip_info['score'] > 0)
//                {
//                    $users_upd_data['score'] = $user_info['score'] + $vip_info['score'];
//                }
                //增加vip的工具次数以及赠送算力
                $this->usersAddVip($vip_order_info['vip_id'],$vip_order_info['users_id']);
                //处理分销
                $this->commission($data['cash_fee'],$vip_order_info['users_id'],$vip_order_info['id']);
                // 记录支付日志
                $users_model->where('id', $vip_order_info['users_id'])->update($users_upd_data);

                // 提交事务
                Db::commit();

                // 标记订单已处理
                return $this->returnXml('SUCCESS', 'OK');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->returnXml('FAIL', '业务逻辑处理失败');
            }
        } else {
            return $this->returnXml('FAIL', '支付结果失败');
        }
    }

    public function commission($money,$users_id,$vip_order_id)
    {
        if($money > 0)
        {
            $money = bcdiv($money,100,2);
            $users_model = new Users();
            $user_info = $users_model->where('id', $users_id)->find();
            $oem_config_model = new Oemconfig();
            $rebateConfig = $oem_config_model->where('group','rebate')->where('admin_id',$user_info['oem_id'])->select();
            $config = [];
            if(!empty($rebateConfig)){
                foreach ($rebateConfig as $pay){
                    $config[$pay['name']] = $pay['value'];
                }
            }
            if($config['one_level'] > 0 && $user_info['parent_id'] > 0)
            {
                $parent_info = $users_model->where('id', $user_info['parent_id'])->find();
                if ($parent_info) {
                    $parent_money = bcdiv(bcmul($money , $config['one_level'],2) , 100,2);
                    $users_model->where('id', $user_info['parent_id'])->setInc('money',$parent_money);
                    $add = [
                        'order_id' => $vip_order_id,
                        'order_type' => 1,
                        'users_id' => $users_id,
                        'parent_id' => $user_info['parent_id'],
                        'income_id' => $user_info['parent_id'],
                        'level' => 1,
                        'rate' => $config['one_level'],
                        'raw_amount' => $money,
                        'computed_amount' => $parent_money,
                        'status' => 2,
                        'oem_id' => $user_info['oem_id'],
                        'agent_id' => $user_info['agent_id']
                    ];
                    $commission_model = new Commissions();
                    $commission_model->save($add);
                }
                $j_parent_info = $users_model->where('id', $user_info['j_parent_id'])->find();
                if ($j_parent_info) {
                    $j_parent_money = bcdiv(bcmul($money , $config['two_level'],2) , 100,2);
                    $users_model->where('id', $user_info['j_parent_id'])->setInc('money',$j_parent_money);
                    $add = [
                        'order_id' => $vip_order_id,
                        'order_type' => 1,
                        'users_id' => $users_id,
                        'parent_id' => $user_info['parent_id'],
                        'income_id' => $user_info['j_parent_id'],
                        'level' => 2,
                        'rate' => $config['two_level'],
                        'raw_amount' => $money,
                        'computed_amount' => $j_parent_money,
                        'status' => 2,
                        'oem_id' => $user_info['oem_id'],
                        'agent_id' => $user_info['agent_id']
                    ];
                    $commission_model = new Commissions();
                    $commission_model->save($add);
                }
            }
        }

    }

    public function usersAddVip($vip_id,$users_id)
    {
        $users_model = new Users();
        $vip_model = new Vip();
        $tools_num_model = new Toolsnum();
        $tools_model = new \app\common\model\Tools();
        $users_info = $users_model->where('id', $users_id)->find();
        $vip_tools_num = $tools_num_model->where('vip_id',$vip_id)->select();
//        dump($vip_tools_num);
//        die();
        $add = [
            'users_id' => $users_id,
            'tools_id' => 0,
            'type' => 2,
            'type_data' => 1,
            'oem_id' => $users_info['oem_id'],
            'agent_id' => $users_info['agent_id'],
        ];
        $sl_log_model = new Sllog();
        $sl_add = [];
        $i = 0;
        foreach ($vip_tools_num as $v){
            if($v['num'] > 0)
            {
                $tools_num_model->where('users_id',$users_id)->where('tools_id',$v['tools_id'])->setInc('num',$v['num']);
                $users_tools_num_before = $tools_num_model->where('users_id',$users_id)->where('tools_id',$v['tools_id'])->value('num');
                $tools_name = $tools_model->where('id',$v['tools_id'])->value('name');
                $add['after'] = $users_tools_num_before + $v['num'];
                $add['before'] = $users_tools_num_before;
                $add['price'] = $v['num'];
                $add['mark'] = '购买套餐-'.$tools_name;
                $sl_add[$i] = $add;
                $i++;
            }
        }

        $sl_log_model->saveAll($sl_add);
        $vip_info = $vip_model->where('id', $vip_id)->find();
        if($vip_info['score'] > 0)
        {
            $users_model->where('id',$users_id)->setInc('score',$vip_info['score']);
        }
    }

    // 将 XML 数据转换为数组
    private function xmlToArray($xml)
    {
        libxml_disable_entity_loader(true);
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }

    // 验证签名
    private function verifySign($data,$key)
    {
        $sign = $data['sign'];
        unset($data['sign']);
//        $key = config('wechat.pay.key'); // 微信支付密钥
        $signStr = $this->getSign($data, $key);
        return $sign == $signStr;
    }

    // 生成签名
    private function getSign($data, $key)
    {
        ksort($data);
        $stringA = http_build_query($data);
        $stringA = urldecode($stringA);
        $stringSignTemp = $stringA . '&key=' . $key;
        return strtoupper(md5($stringSignTemp));
    }

    // 返回 XML 数据给微信
    private function returnXml($return_code, $return_msg)
    {
        $xml = '<xml>
            <return_code><![CDATA[' . $return_code . ']]></return_code>
            <return_msg><![CDATA[' . $return_msg . ']]></return_msg>
        </xml>';
        return $xml;
    }
}
