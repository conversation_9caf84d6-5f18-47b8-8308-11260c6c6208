define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            if(Config.show) {
                var delurl = 'tools/del';
            }else
            {
                var delurl = '';
            }
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'tools/index' + location.search,
                    add_url: 'tools/add',
                    edit_url: 'tools/edit',
                    del_url: delurl,
                    multi_url: 'tools/multi',
                    import_url: 'tools/import',
                    table: 'tools',
                    dragsort_url:'',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'desc', title: __('Desc'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'num', title: __('Num'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'type_data', title: __('Type_data'), searchList: {"1":__('Type_data 1'),"2":__('Type_data 2'),"3":__('Type_data 3'),"4":__('Type_data 4')}, formatter: Table.api.formatter.normal},
                        {field: 'price', title: __('Price'), operate:false},
                        {field: 'pages.name', title: __('Pages_id'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'show_data', title: __('Show_data'), searchList: {"1":__('Show_data 1'),"2":__('Show_data 2')}, formatter: Table.api.formatter.normal},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2')}, formatter: Table.api.formatter.status},
                        {field: 'weigh', title: __('Weigh'), operate: false},
                        // {field: 'createtime', title: __('Createtime'), operate:false, addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'updatetime', title: __('Updatetime'), operate:false, addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'admin.nickname', title: __('Admin_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
