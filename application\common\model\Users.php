<?php

namespace app\common\model;

use think\Model;


class Users extends Model
{

    

    

    // 表名
    protected $name = 'users';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'gender_text',
        'is_vip_text',
        'viptime_text',
        'logintime_text',
        'status_text'
    ];
    

    
    public function getGenderList()
    {
        return ['1' => __('Gender 1'), '2' => __('Gender 2'), '3' => __('Gender 3')];
    }

    public function getIsVipList()
    {
        return ['1' => __('Is_vip 1'), '2' => __('Is_vip 2')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getGenderTextAttr($value, $data)
    {
        $value = $value ?: ($data['gender'] ?? '');
        $list = $this->getGenderList();
        return $list[$value] ?? '';
    }


    public function getIsVipTextAttr($value, $data)
    {
        $value = $value ?: ($data['is_vip'] ?? '');
        $list = $this->getIsVipList();
        return $list[$value] ?? '';
    }


    public function getViptimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['viptime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getLogintimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['logintime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    protected function setViptimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setLogintimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function agent()
    {
        return $this->belongsTo('Admin', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
