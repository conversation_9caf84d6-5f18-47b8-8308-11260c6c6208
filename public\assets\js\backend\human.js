define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'human/index' + location.search,
                    add_url: 'human/add',
                    edit_url: '',
                    del_url: 'human/del',
                    multi_url: 'human/multi',
                    import_url: 'human/import',
                    table: 'human',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id')},
                        {field: 'users.nickname', title: __('Users_id')},
                        {field: 'title', title: __('Title')},
                        {field: 'video', title: __('Video'), operate: false,formatter: function (value,row,index) {
                                // 动态显示年龄列的值，这里可以根据业务需求进行修改
                                return '<video src="' + row.video + '" style="height: 200px;width: 200px" controls></video>'
                            }},
                        // {field: 'cover', title: __('Cover'), operate: false,formatter: function (value,row,index) {
                        //         // 动态显示年龄列的值，这里可以根据业务需求进行修改
                        //         return '<a href="javascript:"><img class=" img-center" src="' + row.video + '?x-oss-process=video/snapshot,t_1,m_fast' + '" style="width: 100px;height: 100px"/></a>'
                        //     }},
                        {field: 'oem.nickname', title: __('Oem_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show},
                        {field: 'agent.nickname', title: __('Agent_id'),operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
