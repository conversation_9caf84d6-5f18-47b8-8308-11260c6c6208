<style type="text/css">
    .sm-st {
        background: #fff;
        padding: 20px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        margin-bottom: 20px;
    }

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }

    .orange {
        background: #fa8564 !important;
    }

    .tar {
        background: #45cf95 !important;
    }

    .sm-st .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow-b {
        background: #fdd752 !important;
    }

    .stat-elem {

        background-color: #fff;
        padding: 18px;
        border-radius: 40px;

    }

    .stat-info {
        text-align: center;
        background-color: #fff;
        border-radius: 5px;
        margin-top: -5px;
        padding: 8px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        font-style: italic;
    }

    .stat-icon {
        text-align: center;
        margin-bottom: 5px;
    }

    .st-red {
        background-color: #F05050;
    }

    .st-green {
        background-color: #27C24C;
    }

    .st-violet {
        background-color: #7266ba;
    }

    .st-blue {
        background-color: #23b7e5;
    }

    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
        float: left;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 5px 0;
    }

    .stat.lg .value {
        font-size: 26px;
        line-height: 28px;
    }

    .stat-col {
        margin:0 0 10px 0;
    }
    .stat.lg .name {
        font-size: 16px;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    .item {
        padding: 30px 0;
    }


    #statistics .panel {
        min-height: 150px;
    }

    #statistics .panel h5 {
        font-size: 14px;
    }
    body{
        background: #f1f4f6;
    }
</style>
<div class="panel panel-default panel-intro" style="background-color:#f2f2f2;">

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">

                <div class="row">
                    {if condition="!$agent_show"}
                    <div class="col-xs-6 col-sm-3 stat-col">
                        <div class="panel panel-default panel-intro panel-statistics">
                            <div class="panel-body">
                                <div class="pull-left">
                                    <h4>小程序码</h4>
<!--                                    <h3>{$count_users}</h3>-->
                                </div>
                                <div class="pull-right" style="color:#c8e3ff;">
                                    <img src="{$agent_qrcode}" style="width: 100px; height: 100px" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                    {/if}
                    <div class="col-xs-6 col-sm-3 stat-col">
                        <div class="panel panel-default panel-intro panel-statistics">
                            <div class="panel-body">
                                <div class="pull-left">
                                    <h4>总用户数</h4>
                                    <h3>{$count_users}</h3>
                                </div>
                                <div class="pull-right" style="color:#c8e3ff;">
                                    <i class="fa fa-users fa-4x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-sm-3 stat-col">
                        <div class="panel panel-default panel-intro panel-statistics">
                            <div class="panel-body">
                                <div class="pull-left">
                                    <h4>今日新增</h4>
                                    <h3>{$day_count_users}</h3>
                                </div>
                                <div class="pull-right" style="color:#ffe9c8;">
                                    <i class="fa fa-user fa-4x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    {if condition="$agent_show"}
                    <div class="col-xs-6 col-sm-3 stat-col">
                        <div class="panel panel-default panel-intro panel-statistics">
                            <div class="panel-body">
                                <div class="pull-left">
                                    <h4>今日收款(含退款)</h4>
                                    <h3>{$day_count_money}</h3>
                                </div>
                                <div class="pull-right" style="color:#ffc8c8;">
                                    <i class="fa fa-cny fa-4x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/if}
                    <div class="col-xs-6 col-sm-3 stat-col">
                        <div class="panel panel-default panel-intro panel-statistics">
                            <div class="panel-body">
                                <div class="pull-left">
                                    <h4>今日工具使用次数</h4>
                                    <h3>{$day_count_tools}</h3>
                                </div>
                                <div class="pull-right" style="color:#c8cfff;">
                                    <i class="fa fa-calendar   fa-4x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row panel" style="background-color:#fff;padding-top:20px;">

                    <div class="col-lg-6">
                        <p>订单数据</p>
                        <div id="echart" class="btn-refresh" style="height:300px;width:100%;"></div>
                    </div>
                    <div class="col-lg-6">
                        <p>工具数据</p>
                        <div id="echart1" class="btn-refresh" style="height:300px;width:100%;"></div>
                    </div>

                </div>

                <div class="row panel" style="background-color:#fff;padding-top:20px;">

                    <div class="col-lg-12">
                        <div style="padding-bottom: 20px;">
                            <a href="javascript:;" class="btn btn-success btn-filter" data-charts="1">今天</a>
                            <a href="javascript:;" class="btn btn-success btn-filter" data-charts="2">昨天</a>
                            <a href="javascript:;" class="btn btn-success btn-filter" data-charts="3">近7天</a>
                            <a href="javascript:;" class="btn btn-success btn-filter" data-charts="4">近30天</a>
                        </div>
                        <div id="echart2" class="btn-refresh" style="height:300px;width:100%;"></div>
                    </div>


                </div>


            </div>
            <div class="tab-pane fade" id="two">
                <div class="row">
                    <div class="col-xs-12">
                        {:__('Custom zone')}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
