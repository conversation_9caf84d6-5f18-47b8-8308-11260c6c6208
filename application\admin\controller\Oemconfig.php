<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\Config as ConfigModel;
use think\Exception;

/**
 * OEM配置信息
 *
 * @icon fa fa-circle-o
 */
class Oemconfig extends Backend
{

    /**
     * Oemconfig模型对象
     * @var \app\common\model\Oemconfig
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Oemconfig;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    /**
     * 查看
     */
    public function index()
    {
        $siteList = [];
        $configList = $this->model->where('admin_id',$this->auth->id)->group('group')->column('group');
        foreach ($configList as $k=>$v) {
            $siteList[$v]['name'] = $v;
            $siteList[$v]['list'] = [];
            if ($k == 0)
            {
                $siteList[$v]['active'] = 1;
            }else
            {
                $siteList[$v]['active'] = 0;
            }

        }
        $oem_config_list = $this->model->where('admin_id',$this->auth->id)->select();
        foreach ($oem_config_list as $k => $v) {
            $value = $v->toArray();
            $value['title'] = __($value['title']);
            if (in_array($value['type'], ['select', 'selects', 'checkbox', 'radio'])) {
                $value['value'] = explode(',', $value['value']);
            }
//            $value['content'] = json_decode($value['content'], true);
            if (in_array($value['name'], ['categorytype', 'configgroup', 'attachmentcategory'])) {
                $dictValue = (array)json_decode($value['value'], true);
                foreach ($dictValue as $index => &$item) {
                    $item = __($item);
                }
                unset($item);
                $value['value'] = json_encode($dictValue, JSON_UNESCAPED_UNICODE);
            }
            $value['tip'] = htmlspecialchars($value['tip'] ?? '');
            if ($value['name'] == 'cdnurl') {
                //cdnurl不支持在线修改
                continue;
            }
            $siteList[$v['group']]['list'][] = $value;
        }
        $index = 0;
        foreach ($siteList as $k => &$v) {
            $v['active'] = !$index ? true : false;
            $index++;
        }
        $color = $this->model->where('admin_id',$this->auth->id)->where('name','xcx_index_color')->value('value');
        if(!$color)
        {
            $color = "#ffffff";
        }
        $this->view->assign('siteList', $siteList);
        $this->view->assign('typeList', ConfigModel::getTypeList());
        $this->view->assign('ruleList', ConfigModel::getRegexList());
        $this->view->assign('groupList', ConfigModel::getGroupList());
        $this->assignconfig('color', $color);
        return $this->view->fetch();
    }




    /**
     * 编辑
     * @param null $ids
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
            $row = $this->request->post("row/a", [], 'trim');
            if ($row) {
                $configList = [];
                $oem_config_list = $this->model->where('admin_id',$this->auth->id)->select();
                foreach ($oem_config_list as $v) {
                    if (isset($row[$v['name']])) {
                        $value = $row[$v['name']];
                        if (is_array($value) && isset($value['field'])) {
                            $value = json_encode(ConfigModel::getArrayData($value), JSON_UNESCAPED_UNICODE);
                        } else {
                            $value = is_array($value) ? implode(',', $value) : $value;
                        }
                        $v['value'] = $value;
                        $configList[] = $v->toArray();
                    }
                }
                try {
                    $this->model->allowField(true)->saveAll($configList);
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                try {
                    ConfigModel::refreshFile();
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
    }

}
