# 第三方服务集成文档

## 服务概述

本系统集成了多个第三方云服务，主要包括微信生态服务、腾讯云AI服务、阿里云存储服务等。所有第三方服务都通过配置化管理，支持多租户独立配置。

## 微信生态服务

### 1. 微信小程序
**SDK版本**：overtrue/wechat ^4.6

**主要功能**：
- 用户授权登录
- 获取用户信息
- 生成小程序二维码
- 消息推送

**配置项**：
```php
// 在fa_oemconfig表中配置
'xcx_appid' => '小程序AppID',
'xcx_appsecret' => '小程序AppSecret',
'xcx_name' => '小程序名称',
'xcx_index_bj' => '首页背景图',
'xcx_index_color' => '首页背景色',
'get_phone' => '是否获取手机号'
```

**代码实现**：
```php
// application/api/controller/User.php
$config = [
    'app_id' => $this->oemConfig['xcx']['xcx_appid'],
    'secret' => $this->oemConfig['xcx']['xcx_appsecret'],
    'response_type' => 'array',
];
$this->xcx_app = Factory::miniProgram($config);

// 微信登录
$session_info = $this->xcx_app->auth->session($code);
```

### 2. 微信支付
**SDK版本**：wechatpay/wechatpay ^1.4

**主要功能**：
- 小程序支付
- 支付回调处理
- 商家转账到零钱
- 订单查询

**配置项**：
```php
'wx_mchid' => '商户号',
'wx_key' => '商户密钥',
'sys_url' => '系统域名'
```

**证书文件**：
- 路径：`/cert/{oem_id}/`
- 文件：`apiclient_cert.pem`、`apiclient_key.pem`

**代码实现**：
```php
// application/common/library/WxPay.php
$config = [
    'app_id' => $this->config['xcx_appid'],
    'mch_id' => $this->config['wx_mchid'],
    'key' => $this->config['wx_key'],
    'cert_path' => '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_cert.pem',
    'key_path' => '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_key.pem',
    'notify_url' => $this->config['sys_url'].'/api/wxpay/wx_notify',
];
$this->payment = Factory::payment($config);
```

## 腾讯云服务

### 1. AI绘画服务 (AiArt)
**SDK版本**：tencentcloud/aiart ^3.0

**主要功能**：
- 文生图（TextToImageLite）
- 图生图（ImageToImage）
- AI写真（SubmitDrawPortraitJob）
- 图像精修（RefineImage）

**配置项**：
```php
'tx_secretid' => '腾讯云SecretId',
'tx_secretkey' => '腾讯云SecretKey'
```

**代码实现**：
```php
// application/service/Utils.php
$cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
$httpProfile = new HttpProfile();
$httpProfile->setEndpoint("aiart.tencentcloudapi.com");
$clientProfile = new ClientProfile();
$clientProfile->setHttpProfile($httpProfile);
$client = new AiartClient($cred, "", $clientProfile);
```

**接口调用示例**：
```php
// AI绘画
public function TextToImageLiteByTx($params, $admin_id)
{
    $req = new TextToImageLiteRequest();
    $req->fromJsonString(json_encode($params));
    $resp = $client->TextToImageLite($req);
    return $this->returnData(1, 'ok', $resp->toJsonString());
}
```

### 2. 语音识别服务 (ASR)
**SDK版本**：tencentcloud/asr ^3.0

**主要功能**：
- 实时语音识别
- 录音文件识别
- 一句话识别

**代码实现**：
```php
$client = new AsrClient($cred, "", $clientProfile);
$req = new SentenceRecognitionRequest();
$req->fromJsonString(json_encode($params));
$resp = $client->SentenceRecognition($req);
```

### 3. OCR文字识别服务
**SDK版本**：tencentcloud/ocr ^3.0

**主要功能**：
- 通用文字识别
- 身份证识别
- 银行卡识别
- 营业执照识别

**代码实现**：
```php
$client = new OcrClient($cred, "", $clientProfile);
$req = new GeneralBasicOCRRequest();
$req->fromJsonString(json_encode($params));
$resp = $client->GeneralBasicOCR($req);
```

### 4. 语音合成服务 (VRS)
**SDK版本**：tencentcloud/vrs ^3.0

**主要功能**：
- 文字转语音
- 多种音色选择
- 语音参数调节

### 5. 混元大模型 (Hunyuan)
**SDK版本**：tencentcloud/hunyuan ^3.0

**主要功能**：
- 文本生成
- 对话聊天
- 内容创作

### 6. 视频理解服务 (VCLM)
**SDK版本**：tencentcloud/vclm ^3.0

**主要功能**：
- 视频内容分析
- 视频标签识别
- 视频摘要生成

### 7. 腾讯云存储 (COS)
**SDK版本**：qcloud/cos-sdk-v5 ^2.6

**主要功能**：
- 文件上传
- 文件下载
- 文件管理

**配置项**：
```php
'tx_secretid' => '腾讯云SecretId',
'tx_secretkey' => '腾讯云SecretKey',
'tx_cos_bucket' => 'COS存储桶',
'tx_cos_region' => 'COS地域',
'tx_cos_urls' => 'COS访问域名'
```

**代码实现**：
```php
$cosClient = new Client([
    'region' => $oss_config['tx_cos_region'],
    'schema' => 'https',
    'credentials' => [
        'secretId' => $oss_config['tx_secretid'],
        'secretKey' => $oss_config['tx_secretkey']
    ]
]);
```

## 阿里云服务

### 1. 对象存储 OSS
**SDK版本**：aliyuncs/oss-sdk-php ^2.7

**主要功能**：
- 文件上传
- 文件下载
- 文件管理
- CDN加速

**配置项**：
```php
'access_key_id' => '阿里云AccessKey ID',
'access_key_secret' => '阿里云AccessKey Secret',
'oss_bucket' => 'OSS存储桶',
'oss_endpoint' => 'OSS节点地址',
'oss_urls' => 'OSS访问域名'
```

**代码实现**：
```php
// application/service/Utils.php
$provider = new StaticCredentialsProvider(
    $oss_config['access_key_id'],
    $oss_config['access_key_secret']
);
$config = array(
    "provider" => $provider,
    "endpoint" => $endpoint,
    "signatureVersion" => OssClient::OSS_SIGNATURE_VERSION_V4,
    "region" => $region
);
$ossClient = new OssClient($config);
$result = $ossClient->uploadFile($oss_config['oss_bucket'], $obj_path, $file_path);
```

## 百度云服务

### 1. 千帆大模型
**主要功能**：
- AI对话
- 文本生成
- 内容创作

**配置项**：
```php
'bd_v2_appid' => '百度云AppID',
'bd_v2_apikey' => '百度云API Key'
```

**代码实现**：
```php
$chat_url = 'https://qianfan.baidubce.com/v2/chat/completions';
$header = [
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'appid: '.$bd_config['bd_v2_appid'],
        'Authorization: Bearer '.$bd_config['bd_v2_apikey']
    ]
];
```

## 配置管理

### 1. 配置存储
所有第三方服务配置都存储在`fa_oemconfig`表中，按照以下分组：

| 分组 | 说明 | 配置项 |
|------|------|--------|
| xcx | 小程序配置 | appid、secret、名称等 |
| wxpay | 微信支付配置 | 商户号、密钥等 |
| tencent | 腾讯云配置 | SecretId、SecretKey等 |
| aliyun | 阿里云配置 | AccessKey、存储配置等 |
| baiduyun | 百度云配置 | AppID、API Key等 |
| system | 系统配置 | 域名、通知等 |

### 2. 配置获取
```php
// application/service/Utils.php
public function getOemConfig($admin_id, $group)
{
    $oem_config_model = new Oemconfig();
    $config_list = $oem_config_model
        ->where('admin_id', $admin_id)
        ->where('group', $group)
        ->select();
    
    $config = [];
    foreach ($config_list as $item) {
        $config[$item['name']] = $item['value'];
    }
    return $config;
}
```

### 3. 多租户支持
每个租户（OEM/代理）都可以配置独立的第三方服务参数，实现数据和服务的完全隔离。

## 错误处理

### 1. 统一错误处理
```php
try {
    // 调用第三方服务
    $result = $client->someMethod($request);
    return $this->returnData(1, 'ok', $result);
} catch (TencentCloudSDKException $e) {
    return $this->returnData(0, $e->getMessage());
} catch (OssException $e) {
    return $this->returnData(0, $e->getMessage());
}
```

### 2. 服务降级
当第三方服务不可用时，系统会返回友好的错误信息，不会影响其他功能的正常使用。

## 监控和日志

### 1. 调用日志
所有第三方服务调用都会记录日志，包括：
- 调用时间
- 调用参数
- 返回结果
- 错误信息

### 2. 性能监控
- 接口响应时间监控
- 成功率统计
- 错误率分析

## 安全考虑

### 1. 密钥管理
- 所有密钥都加密存储
- 定期更换密钥
- 权限最小化原则

### 2. 网络安全
- 使用HTTPS协议
- IP白名单限制
- 请求签名验证

### 3. 数据安全
- 敏感数据加密传输
- 临时文件及时清理
- 访问日志记录

## 扩展指南

### 1. 新增第三方服务
1. 安装对应的SDK包
2. 在`fa_oemconfig`表中添加配置项
3. 在`Utils`服务中添加调用方法
4. 在控制器中调用服务方法

### 2. 配置新的租户
1. 在后台管理系统中添加配置项
2. 上传必要的证书文件
3. 测试服务连通性

---

**更新日期**：2025年1月  
**文档版本**：v1.0
