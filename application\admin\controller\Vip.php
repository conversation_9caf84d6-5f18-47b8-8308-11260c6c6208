<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\Tools;
use app\common\model\Toolsnum;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * vip管理
 *
 * @icon fa fa-circle-o
 */
class Vip extends Backend
{

    /**
     * Vip模型对象
     * @var \app\common\model\Vip
     */
    protected $model = null;
    protected $dataLimit = 'auth'; //默认基类中为false，表示不启用，可额外使用auth和personal两个值
    protected $dataLimitField = 'admin_id'; //数据关联字段,当前控制器对应的模型表中必须存在该字段

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Vip;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $oem_where = [];
            if ($this->dataLimit && $this->dataLimitFieldAutoFill && $this->auth->id != 1) {
                $oem_where['vip.'.$this->dataLimitField] = $this->auth->id;
            }
            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where($oem_where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('admin')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            $new_id = $this->model->id;
//            dump($new_id);
            //VIP，自动新增工具次数
            $tools_model = new Tools();
            $tools_list = $tools_model->where('admin_id',$this->auth->id)->select();
            $toolsnum_model = new Toolsnum();
            if(!empty($tools_list))
            {
                foreach ($tools_list as $k => $v) {
                    $add_data[$k] = [
                        'vip_id' => $new_id,
                        'tools_id' => $v['id'],
                        'num' => 0,
                    ];
                }
                if(!empty($add_data))
                {
                    $ret = $toolsnum_model->saveAll($add_data);
                    if($ret === false)
                    {
                        exception($toolsnum_model->getError());
                    }
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    //修改会员默认次数
    public function tools($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $toolsnum_model = new \app\common\model\Toolsnum();

        //获取当前用户所属贴牌的所有工具
        $tools_model = new \app\common\model\Tools();
        $tools_list = $tools_model->where('admin_id',$row['admin_id'])->select();
        //获取VIP所有工具的次数
        $vip_num = $toolsnum_model->where('vip_id',$ids)->select();
        if (false === $this->request->isPost()) {
            $this->view->assign('tools_list', $tools_list);
            $this->view->assign('vip_num', $vip_num);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $result = false;
        Db::startTrans();
        try {
            //循环参数，判断是否为代理、 代理次数是否足够
            foreach ($params as $k=>$v)
            {
                if(is_numeric($v) )
                {
                    $result = $toolsnum_model->where('id',$k)->where('vip_id',$row['id'])->update(['num'=>$v]);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
