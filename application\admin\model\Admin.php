<?php

namespace app\admin\model;

use think\Model;
use think\Session;

class Admin extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $hidden = [
        'password',
        'salt'
    ];

    // 追加属性
    protected $append = [
        'admin_type_text'
    ];

    public static function init()
    {
        self::beforeWrite(function ($row) {
            $changed = $row->getChangedData();
            //如果修改了用户或或密码则需要重新登录
            if (isset($changed['username']) || isset($changed['password']) || isset($changed['salt'])) {
                $row->token = '';
            }
        });
    }


    public function getAdminTypeList()
    {
        return ['1' => __('Admin_type 1'), '2' => __('Admin_type 2'), '3' => __('Admin_type 3')];
    }


    public function getAdminTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['admin_type'] ?? '');
        $list = $this->getAdminTypeList();
        return $list[$value] ?? '';
    }

}
