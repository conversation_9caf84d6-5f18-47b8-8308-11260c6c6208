# API接口文档

## 接口概述

本系统提供RESTful风格的API接口，主要用于微信小程序与后台系统的数据交互。所有接口均返回JSON格式数据，遵循统一的响应格式。

### 基础信息
- **接口域名**：https://szr-xcx.sanliankj.com.cn
- **接口前缀**：/api
- **请求方式**：GET/POST
- **数据格式**：JSON
- **字符编码**：UTF-8

### 统一响应格式
```json
{
    "code": 1,           // 状态码：1成功，0失败
    "msg": "请求成功",    // 响应消息
    "time": "1640995200", // 时间戳
    "data": {}           // 响应数据
}
```

### 公共参数
所有接口都需要传递以下参数：
- `oem_id` 或 `agent_code`：租户标识，用于多租户数据隔离
- `token`：用户认证令牌（需要登录的接口）

## 用户相关接口 (User)

### 1. 微信登录
**接口地址**：`/api/user/wxLogin`  
**请求方式**：POST  
**是否需要登录**：否

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信授权码 |
| scene | string | 否 | 场景值 |
| agent_code | string | 是 | 代理商编码 |

**响应示例**：
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "userinfo": {
            "id": 1,
            "nickname": "用户昵称",
            "avatar": "头像地址"
        },
        "token": "用户token"
    }
}
```

### 2. 更新用户信息
**接口地址**：`/api/user/updateUsers`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| nickname | string | 否 | 用户昵称 |
| image | string | 否 | 用户头像 |

### 3. 获取用户二维码
**接口地址**：`/api/user/getUserQrcode`  
**请求方式**：POST  
**是否需要登录**：是

**功能说明**：生成用户专属的小程序二维码，用于分享推广

## 首页相关接口 (Index)

### 1. 获取首页配置
**接口地址**：`/api/index/getIndexConfig`  
**请求方式**：GET  
**是否需要登录**：否

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agent_code | string | 是 | 代理商编码 |

**响应示例**：
```json
{
    "code": 1,
    "msg": "请求成功",
    "data": {
        "index_config": {
            "index_bg": "背景图片地址",
            "index_bg_color": "#ffffff",
            "xcx_name": "小程序名称",
            "top_img": {
                "id": 1,
                "image": "顶部图片地址"
            }
        },
        "banner_list": [
            {
                "id": 1,
                "title": "轮播图标题",
                "image": "图片地址",
                "url": "跳转链接"
            }
        ],
        "tools_list": [
            {
                "id": 1,
                "name": "工具名称",
                "icon": "工具图标",
                "desc": "工具描述"
            }
        ]
    }
}
```

### 2. 获取小程序用户信息
**接口地址**：`/api/index/getXcxUserInfo`  
**请求方式**：GET  
**是否需要登录**：是

**响应示例**：
```json
{
    "code": 1,
    "msg": "ok",
    "data": {
        "id": 1,
        "nickname": "用户昵称",
        "avatar": "头像地址",
        "score": 100,
        "viptime": "2024-12-31",
        "tools": [
            {
                "name": "AI绘画",
                "num": 10
            }
        ],
        "get_phone": 1
    }
}
```

## 工具相关接口 (Tools)

### 1. AI绘画
**接口地址**：`/api/tools/createImage`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| prompt | string | 是 | 绘画提示词 |
| style | string | 否 | 绘画风格 |
| size | string | 否 | 图片尺寸 |

### 2. AI写真
**接口地址**：`/api/tools/createPortrait`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| images | array | 是 | 用户照片数组 |
| aiStyleId | string | 是 | AI风格ID |

### 3. 语音识别
**接口地址**：`/api/tools/speechRecognition`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| audio_url | string | 是 | 音频文件地址 |
| format | string | 否 | 音频格式 |

### 4. OCR文字识别
**接口地址**：`/api/tools/ocrRecognition`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image_url | string | 是 | 图片地址 |
| scene | string | 否 | 识别场景 |

### 5. 语音合成
**接口地址**：`/api/tools/createAudio`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 音频标题 |
| url | string | 是 | 文本内容或音频地址 |

## 支付相关接口 (Wxpay)

### 1. 创建支付订单
**接口地址**：`/api/wxpay/createOrder`  
**请求方式**：POST  
**是否需要登录**：是

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 订单类型：vip/tools |
| product_id | int | 是 | 产品ID |
| amount | decimal | 是 | 支付金额 |

**响应示例**：
```json
{
    "code": 1,
    "msg": "创建成功",
    "data": {
        "order_no": "订单号",
        "pay_info": {
            "timeStamp": "时间戳",
            "nonceStr": "随机字符串",
            "package": "prepay_id=xxx",
            "signType": "MD5",
            "paySign": "签名"
        }
    }
}
```

### 2. 支付回调
**接口地址**：`/api/wxpay/wx_notify`  
**请求方式**：POST  
**是否需要登录**：否

**功能说明**：微信支付成功后的回调接口，用于更新订单状态

## 文件上传接口 (Upload)

### 1. 获取OSS配置
**接口地址**：`/api/upload/getSets`  
**请求方式**：GET  
**是否需要登录**：否

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agent_code | string | 是 | 代理商编码 |

**响应示例**：
```json
{
    "code": 1,
    "msg": "",
    "data": {
        "accessId": "阿里云AccessKey ID",
        "accessSecret": "阿里云AccessKey Secret",
        "bucket": "存储桶名称",
        "endpoint": "OSS节点地址",
        "url": "访问域名"
    }
}
```

## 视频相关接口 (Video)

### 1. 获取任务列表
**接口地址**：`/api/video/get_tasks`  
**请求方式**：GET  
**是否需要登录**：否

### 2. 提交任务
**接口地址**：`/api/video/submit_task`  
**请求方式**：POST  
**是否需要登录**：否

### 3. 保存音色
**接口地址**：`/api/video/save_yinse`  
**请求方式**：POST  
**是否需要登录**：否

### 4. 保存人物
**接口地址**：`/api/video/save_human`  
**请求方式**：POST  
**是否需要登录**：否

### 5. 保存音频
**接口地址**：`/api/video/save_audio`  
**请求方式**：POST  
**是否需要登录**：否

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1 | 请求成功 |
| 0 | 请求失败 |
| 401 | 未登录或token无效 |
| 403 | 无权限访问 |
| 404 | 接口不存在 |
| 500 | 服务器内部错误 |

## 接口调用示例

### JavaScript调用示例
```javascript
// 微信登录
wx.login({
    success: function(res) {
        if (res.code) {
            wx.request({
                url: 'https://szr-xcx.sanliankj.com.cn/api/user/wxLogin',
                method: 'POST',
                data: {
                    code: res.code,
                    agent_code: 'your_agent_code'
                },
                success: function(response) {
                    if (response.data.code === 1) {
                        // 登录成功，保存token
                        wx.setStorageSync('token', response.data.data.token);
                    }
                }
            });
        }
    }
});

// 调用需要登录的接口
wx.request({
    url: 'https://szr-xcx.sanliankj.com.cn/api/index/getXcxUserInfo',
    method: 'GET',
    header: {
        'token': wx.getStorageSync('token')
    },
    data: {
        agent_code: 'your_agent_code'
    },
    success: function(response) {
        console.log(response.data);
    }
});
```

## 注意事项

1. **多租户支持**：所有接口都需要传递`agent_code`参数来标识租户
2. **Token验证**：需要登录的接口必须在请求头或参数中传递有效的token
3. **频率限制**：部分接口可能有调用频率限制，请合理控制请求频率
4. **数据格式**：请求和响应数据均为JSON格式，请设置正确的Content-Type
5. **错误处理**：请根据返回的code字段判断请求是否成功，并处理相应的错误情况
6. **HTTPS**：生产环境建议使用HTTPS协议确保数据传输安全

---

**更新日期**：2025年1月  
**文档版本**：v1.0
