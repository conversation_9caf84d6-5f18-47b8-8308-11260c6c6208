# 数字人AI视频创作功能实现运行逻辑

## 功能概述

数字人AI视频创作功能是系统的核心AI服务之一，主要实现用户上传数字人视频模板，然后输入文本内容，系统通过AI技术将文本转换为语音，并与数字人视频合成，生成数字人说话的视频。该功能主要基于腾讯云语音合成服务和第三方视频合成服务实现。

## 技术架构

### 核心组件
- **前端**：微信小程序
- **后端API**：FastAdmin + ThinkPHP 5.1
- **AI服务**：腾讯云语音合成服务
- **视频合成**：第三方视频合成服务
- **存储服务**：阿里云OSS / 腾讯云COS
- **数据库**：MySQL（fa_human表、fa_toolsorder表）

### 主要数据表
- `fa_human`：数字人模板表
- `fa_toolsorder`：工具使用订单表（rule='ai_shipin'）
- `fa_users`：用户表

### 工具规则标识
- `ai_shipin`：AI视频创作（数字人说话视频合成）
- `ai_yinpin`：AI语音合成
- `ai_yinse`：AI音色克隆

## 完整业务流程

### 1. 数字人模板上传流程

#### 1.1 用户上传数字人视频
```mermaid
sequenceDiagram
    participant 小程序 as 微信小程序
    participant API as API服务
    participant OSS as 云存储
    participant DB as 数据库

    小程序->>API: 上传视频文件
    API->>OSS: 上传到云存储
    OSS-->>API: 返回视频URL
    API->>DB: 保存数字人记录到fa_human表
    API-->>小程序: 返回上传成功
```

**接口调用**：`/api/index/createHuman`

**关键代码实现**：
```php
// application/api/controller/Index.php
public function createHuman()
{
    $params = $this->request->param();
    $user_info = $this->auth->getUserInfo();
    
    if($params['video'] && $params['title']){
        $add = [
            'users_id' => $user_info['id'],
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'title' => $params['title'],
            'video' => $params['video'],
            'cover' => $params['video'].'?x-oss-process=video/snapshot,t_1,m_fast',
            'power' => $user_info['power']
        ];
        $human_model = new Human();
        $res = $human_model->save($add);
        
        if($res){
            $this->success('添加成功');
        }else{
            $this->error('添加失败');
        }
    }else{
        $this->error('参数不完整');
    }
}
```

**执行流程**：
1. 接收前端传递的视频URL和标题
2. 获取当前登录用户信息
3. 构造数字人记录数据
4. 自动生成视频封面（OSS视频截图功能）
5. 保存到fa_human表
6. 返回操作结果

### 2. 数字人AI视频创作流程

#### 2.1 用户提交视频合成任务
```mermaid
sequenceDiagram
    participant 小程序 as 微信小程序
    participant API as API服务
    participant 权限检查 as 权限验证
    participant 第三方 as 第三方视频合成服务
    participant DB as 数据库

    小程序->>API: 提交AI视频创作任务
    API->>权限检查: 检查用户权限和余额
    权限检查-->>API: 权限验证通过
    API->>DB: 创建工具订单记录(rule='ai_shipin')
    API-->>小程序: 返回任务提交成功

    Note over API,第三方: 异步处理流程
    API->>第三方: 通过队列获取任务
    第三方->>API: 处理完成回调
    API->>DB: 更新订单状态
```

**接口调用**：`/api/tools/createVideo`

**关键代码实现**：
```php
// application/api/controller/Tools.php
public function createVideo()
{
    $user_info = $this->auth->getUserinfo();
    $params = $this->request->post();
    $tools_order_model = new Toolsorder();
    $tools_model = new \app\common\model\Tools();

    // 获取AI视频工具信息
    $tools_info = $tools_model->where('admin_id',$this->oemId)
                             ->where('rule','ai_shipin')
                             ->find();

    if(!$tools_info){
        $this->error('工具暂时无法使用');
    }

    // 权限检查
    $inspect = new Inspect();
    $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
    if(!$tools_type){
        $this->error('算力不足');
    }
    
    Db::startTrans();
    try {
        // 创建订单
        $add = [
            'name' => $tools_info['name'],
            'users_id' => $user_info['id'],
            'tools_id' => $tools_info['id'],
            'paydata' => 1,
            'price' => $tools_info['price'],
            'content' => json_encode($params),
            'status' => 1,
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'tools_rule' => $tools_info['rule']
        ];
        
        $tools_order_result = $tools_order_model->save($add);
        $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
        
    Db::startTrans();
    try {
        // 创建订单记录
        $add = [
            'name' => $tools_info['name'],
            'users_id' => $user_info['id'],
            'tools_id' => $tools_info['id'],
            'paydata' => 1,
            'price' => $tools_info['price'],
            'content' => json_encode($params),
            'status' => 1, // 处理中状态
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'tools_rule' => $tools_info['rule']
        ];

        $tools_order_result = $tools_order_model->save($add);

        // 扣减用户余额/次数
        $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);

        Db::commit();
        $this->success('提交成功，正在处理中...');
    }catch (\Exception $e){
        Db::rollback();
        $this->error($e->getMessage());
    }
}
```

**执行流程**：
1. 接收前端传递的参数（包含文本内容、数字人ID等）
2. 获取AI视频工具配置信息
3. 检查用户权限（VIP状态、工具次数、积分余额）
4. 创建工具订单记录，状态设为处理中
5. 扣减用户余额或次数
6. 返回提交成功，任务进入异步处理队列

#### 2.2 异步任务处理流程

**任务队列获取**：第三方服务通过`/api/video/get_tasks`接口获取待处理任务

```php
// application/api/controller/Video.php
public function get_tasks()
{
    $oem_id = $this->request->param('oem_id');
    $tools_order_model = new Toolsorder();

    // 查询待处理的AI视频任务
    $where = [
        'oem_id' => $oem_id,
        'tools_rule' => 'ai_shipin',
        'status' => 1  // 处理中状态
    ];

    $tools_order_list = $tools_order_model->where($where)->limit(5)->order('id asc')->select();
    $tasks = [];
    $human_model = new Human();

    foreach ($tools_order_list as $k => $v) {
        if($v['tools_rule'] == 'ai_shipin') {
            $content = json_decode($v['content'],true);
            $content = json_decode($content['text'],true);

            // 获取数字人信息
            $human_info = $human_model->where('id',$content['aiAvatarId'])->find();
            if($human_info) {
                $tasks[$k]['id'] = $v['id'];
                $tasks[$k]['asr_format_audio_url'] = $content['asr_format_audio_url'];
                $tasks[$k]['reference_audio_text'] = $content['reference_audio_text'];
                $tasks[$k]['audio_text'] = $content['text'];
                $tasks[$k]['extra'] = NULL;
                $tasks[$k]['video_url'] = $human_info['video'];
                $tasks[$k]['rule'] = 'ai_shipin';
            }
        }
    }

    $data = [
        'tasks' => $tasks,
        'backgrounds' => []
    ];
    $this->success('', $data, 200);
}
```
    
#### 2.3 语音合成处理

**命令行任务处理**：系统通过命令行任务处理视频合成请求

```php
// application/admin/command/Video.php
public function handle(Input $input, Output $output)
{
    $tools_order_model = new Toolsorder();
    $where = [
        'tools_rule' => 'ai_shipin',
        'status' => 1
    ];
    $tools_order_list = $tools_order_model->where($where)->select();

    foreach ($tools_order_list as $v) {
        $human_model = new Human();
        $utils = new Utils();
        $content = json_decode($v['content'],true);
        $content_arr = json_decode($content['text'],true);
        $human_info = $human_model->where('id',$content_arr['aiAvatarId'])->find();

        if(!empty($human_info)){
            // 先生成音频
            $res = $utils->createAudio([
                'text' => $content_arr['text'],
                'FastVoiceType' => $content_arr['FastVoiceType']
            ], $v['oem_id']);

            if($res['code'] == 1){
                // 音频生成成功，准备合成视频
                $success = [
                    'audio' => $res['data']['url'],
                    'video' => $human_info['video'],
                ];
                $upd_data = [
                    'success' => json_encode($success),
                ];
                $tools_order_model->where('id', $v['id'])->update($upd_data);
            } else {
                // 音频生成失败
                $error = [
                    'text' => "生成音频失败".json_encode($res),
                ];
                $upd_data = [
                    'error' => json_encode($error),
                    'status' => 3,
                    'endtime' => time(),
                ];
                $tools_order_model->where('id', $v['id'])->update($upd_data);
            }
        }
    }
}
```
```

#### 2.4 腾讯云语音合成服务

**服务类方法**：`Utils::createAudio()`

```php
// application/service/Utils.php
public function createAudio($params,$admin_id)
{
    $oem_status = $this->checkOem($admin_id);
    if(!$oem_status) {
        return $this->returnData(0,'当前被禁用');
    }

    $tx_config = $this->getOemConfig($admin_id,'tencent');
    if(!$tx_config) {
        return $this->returnData(0,'暂无配置');
    }

    $text = $params['text'];
    $voiceType = 200000000; // 语音类型
    $sessionId = uniqid();
    $timestamp = time();
    $expired = $timestamp + 3600; // 签名过期时间

    // 生成签名
    $stringToSign = "GETtts.cloud.tencent.com/stream_ws?Action=TextToStreamAudioWS&AppId=".$tx_config['tx_appid']."&Codec=mp3&Expired=$expired&FastVoiceType=".$params['FastVoiceType']."&SecretId=".$tx_config['tx_secretid']."&SessionId=$sessionId&Text=$text&Timestamp=$timestamp&VoiceType=$voiceType";

    $sign = base64_encode(hash_hmac('sha1', $stringToSign, $tx_config['tx_secretkey'], true));

    // 构造WebSocket连接URL
    $url = "wss://tts.cloud.tencent.com/stream_ws?Action=TextToStreamAudioWS&AppId=".$tx_config['tx_appid']."&Codec=mp3&Expired=$expired&FastVoiceType=".$params['FastVoiceType']."&SecretId=".$tx_config['tx_secretid']."&SessionId=$sessionId&Text=$text&Timestamp=$timestamp&VoiceType=$voiceType&Signature=".urlencode($sign);

    // 调用语音合成服务
    // ... 具体实现逻辑

    return $this->returnData(1,'ok', ['url' => $audioUrl]);
}
```

### 3. 视频合成结果处理

#### 3.1 第三方服务回调处理
**回调接口**：`/api/video/submit_task`

```php
// application/api/controller/Video.php
public function submit_task()
{
    $params = $this->request->post();
    $tools_order_model = new Toolsorder();

    if (!empty($params['url']) && $params['status'] == 3) {
        // 视频合成成功回调
        $success = [
            'video' => $params['url'],
        ];
        $upd_data = [
            'success' => json_encode($success),
            'status' => 2,  // 完成状态
            'endtime' => time()
        ];
        $tools_order_model->where('id',$params['id'])->update($upd_data);
    } else {
        // 视频合成失败回调
        $error = [
            'text' => '视频合成失败',
        ];
        $upd_data = [
            'error' => json_encode($error),
            'status' => 3,  // 失败状态
            'endtime' => time()
        ];
        $tools_order_model->where('id',$params['id'])->update($upd_data);
    }

    $this->success('ok',['status' => 1],200);
}
```

**执行流程**：
1. 接收第三方服务的回调请求
2. 根据回调状态更新订单状态
3. 成功时保存视频URL到success字段
4. 失败时保存错误信息到error字段
5. 更新订单完成时间

#### 3.2 数字人音色保存
**接口**：`/api/video/save_human`

```php
// application/api/controller/Video.php
public function save_human()
{
    $params = $this->request->post();
    $human_model = new Human();

    // 更新数字人音色信息
    $upd_data = [
        'reference_audio_text' => $params['reference_audio_text'] ?? '',
        'asr_format_audio_url' => $params['asr_format_audio_url'] ?? '',
        'audio' => $params['audio'] ?? '',
        'status' => $params['status']
    ];

    $human_model->where('id',$params['id'])->update($upd_data);
    $this->success('ok',['status' => 1],200);
}
```

**功能说明**：
- 保存数字人的音色信息
- 包括参考音频文本、音频URL等
- 用于后续的语音合成

## 完整数据流转图

```mermaid
graph TD
    A[用户上传数字人视频] --> B[保存到fa_human表]
    C[用户提交AI视频创作任务] --> D[创建fa_toolsorder订单]
    D --> E[扣减用户余额/次数]
    E --> F[任务进入处理队列]

    G[第三方服务获取任务] --> H[/api/video/get_tasks]
    H --> I[返回待处理任务列表]
    I --> J[获取数字人视频和文本内容]

    J --> K[调用腾讯云语音合成]
    K --> L[生成语音文件]
    L --> M[数字人视频+语音合成]
    M --> N[生成最终视频]

    N --> O[回调/api/video/submit_task]
    O --> P[更新订单状态为完成]

    Q[处理失败] --> R[回调错误状态]
    R --> S[更新订单状态为失败]
```

## 关键接口调用链路

### 1. 用户操作流程
```
用户上传数字人视频 → /api/index/createHuman → fa_human表
用户提交AI视频创作 → /api/tools/createVideo → fa_toolsorder表
```

### 2. 异步处理流程
```
第三方服务 → /api/video/get_tasks → 获取待处理任务
第三方服务 → 调用腾讯云语音合成 → 生成语音文件
第三方服务 → 数字人视频合成 → 生成最终视频
第三方服务 → /api/video/submit_task → 更新订单状态
```

### 3. 数据表状态变化
```
fa_toolsorder.status:
1 → 处理中（任务提交后）
2 → 完成（视频合成成功）
3 → 失败（处理失败）
```

## 关键技术点

### 1. 腾讯云语音合成服务
- **服务名称**：Text To Speech (TTS)
- **主要功能**：将文本转换为语音
- **支持特性**：
  - 多种音色选择（FastVoiceType参数）
  - 自定义音色克隆
  - 实时语音合成
  - WebSocket流式传输

### 2. 异步处理机制
- 任务提交后立即返回，不等待处理完成
- 通过队列机制管理任务处理
- 第三方服务主动获取任务进行处理
- 支持回调通知机制更新任务状态

### 3. 数字人视频合成
- 用户上传的数字人视频作为模板
- 系统生成的语音文件作为音频源
- 第三方服务负责视频和音频的合成
- 生成数字人说话的最终视频

### 4. 多租户数据隔离
- 每个OEM拥有独立的腾讯云配置
- 任务队列按OEM进行隔离
- 数字人模板按用户和OEM隔离

### 5. 错误处理和容错
- 权限验证：检查用户VIP状态、余额和权限
- 数据验证：检查数字人是否存在
- 事务回滚：确保数据一致性
- 失败重试：支持任务失败后的状态更新

## 业务流程总结

### 完整处理链路
1. **用户上传数字人视频** → `createHuman()` → 保存到`fa_human`表
2. **用户提交AI视频创作任务** → `createVideo()` → 创建`fa_toolsorder`订单
3. **第三方服务获取任务** → `get_tasks()` → 返回待处理任务列表
4. **语音合成处理** → 调用腾讯云TTS → 生成语音文件
5. **视频合成处理** → 数字人视频+语音 → 生成最终视频
6. **结果回调** → `submit_task()` → 更新订单状态

### 核心数据结构
```json
// fa_toolsorder.content 字段存储的任务参数
{
    "text": "{\"text\":\"要合成的文本内容\",\"aiAvatarId\":\"数字人ID\",\"FastVoiceType\":\"音色类型\",\"asr_format_audio_url\":\"参考音频\",\"reference_audio_text\":\"参考文本\"}"
}

// fa_toolsorder.success 字段存储的结果
{
    "video": "生成的视频URL"
}
```

### 状态流转
- **status=1**：处理中，任务已提交等待处理
- **status=2**：完成，视频合成成功
- **status=3**：失败，处理过程中出现错误

## 扩展和优化建议

### 1. 性能优化
- 实现任务队列的负载均衡
- 增加语音合成结果缓存
- 优化数字人视频存储和传输

### 2. 功能扩展
- 支持更多语音合成选项
- 增加视频特效和滤镜
- 支持批量视频生成

### 3. 监控和运维
- 增加任务处理时长监控
- 实现失败任务自动重试
- 添加详细的错误日志记录

---

**更新日期**：2025年1月28日  
**文档版本**：v1.0
