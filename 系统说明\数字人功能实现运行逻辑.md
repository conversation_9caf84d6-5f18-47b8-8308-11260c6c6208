# 数字人功能实现运行逻辑

## 功能概述

数字人视频生成功能是系统的核心AI服务之一，主要实现用户上传数字人视频模板，然后通过AI技术合成个性化的数字人视频。该功能基于腾讯云VCLM（Video Content and Live Media）服务实现，支持图片跳舞、数字人说话等多种视频生成模式。

## 技术架构

### 核心组件
- **前端**：微信小程序
- **后端API**：FastAdmin + ThinkPHP 5.1
- **AI服务**：腾讯云VCLM服务
- **存储服务**：阿里云OSS / 腾讯云COS
- **数据库**：MySQL（fa_human表、fa_toolsorder表）

### 主要数据表
- `fa_human`：数字人模板表
- `fa_toolsorder`：工具使用订单表
- `fa_users`：用户表

## 完整业务流程

### 1. 数字人模板上传流程

#### 1.1 用户上传数字人视频
```mermaid
sequenceDiagram
    participant 小程序 as 微信小程序
    participant API as API服务
    participant OSS as 云存储
    participant DB as 数据库

    小程序->>API: 上传视频文件
    API->>OSS: 上传到云存储
    OSS-->>API: 返回视频URL
    API->>DB: 保存数字人记录到fa_human表
    API-->>小程序: 返回上传成功
```

**接口调用**：`/api/index/createHuman`

**关键代码实现**：
```php
// application/api/controller/Index.php
public function createHuman()
{
    $params = $this->request->param();
    $user_info = $this->auth->getUserInfo();
    
    if($params['video'] && $params['title']){
        $add = [
            'users_id' => $user_info['id'],
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'title' => $params['title'],
            'video' => $params['video'],
            'cover' => $params['video'].'?x-oss-process=video/snapshot,t_1,m_fast',
            'power' => $user_info['power']
        ];
        $human_model = new Human();
        $res = $human_model->save($add);
        
        if($res){
            $this->success('添加成功');
        }else{
            $this->error('添加失败');
        }
    }else{
        $this->error('参数不完整');
    }
}
```

**执行流程**：
1. 接收前端传递的视频URL和标题
2. 获取当前登录用户信息
3. 构造数字人记录数据
4. 自动生成视频封面（OSS视频截图功能）
5. 保存到fa_human表
6. 返回操作结果

### 2. 数字人视频合成流程

#### 2.1 用户提交视频合成任务
```mermaid
sequenceDiagram
    participant 小程序 as 微信小程序
    participant API as API服务
    participant 权限检查 as 权限验证
    participant 腾讯云 as 腾讯云VCLM
    participant DB as 数据库

    小程序->>API: 提交合成任务
    API->>权限检查: 检查用户权限和余额
    权限检查-->>API: 权限验证通过
    API->>DB: 创建工具订单记录
    API->>腾讯云: 调用VCLM图片跳舞接口
    腾讯云-->>API: 返回任务ID
    API->>DB: 更新订单状态和任务ID
    API-->>小程序: 返回任务提交成功
```

**接口调用**：`/api/tools/createImageAnimate`

**关键代码实现**：
```php
// application/api/controller/Tools.php
public function createImageAnimate()
{
    $user_info = $this->auth->getUserinfo();
    $tools_model = new \app\common\model\Tools();
    $tools_order_model = new Toolsorder();
    $users_model = new Users();
    
    // 获取工具信息
    $tools_info = $tools_model->where('admin_id',$this->oemId)
                             ->where('rule','ai_shipin')
                             ->find();
    
    if(!$tools_info){
        $this->error('工具暂时无法使用');
    }
    
    // 权限检查
    $inspect = new Inspect();
    $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
    if (!$tools_type){
        $this->error('算力不足');
    }
    
    $params = $this->request->post();
    
    Db::startTrans();
    try {
        // 创建订单
        $add = [
            'name' => $tools_info['name'],
            'users_id' => $user_info['id'],
            'tools_id' => $tools_info['id'],
            'paydata' => 1,
            'price' => $tools_info['price'],
            'content' => json_encode($params),
            'status' => 1,
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'tools_rule' => $tools_info['rule']
        ];
        
        $tools_order_result = $tools_order_model->save($add);
        $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
        
        // 扣减用户余额/次数
        $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
        
        // 调用腾讯云VCLM服务
        $send_params = [
            'ImageUrl' => $params['url'],
            'TemplateId' => $params['style'],
        ];
        $result_upd = $utils->createImageAnimateJob($send_params,$this->oemId);
        
        if($result_upd['code'] == 1) {
            $upd_data = [
                'success' => $result_upd['data'],
                'status' => 1
            ];
            $res = $tools_order_info->save($upd_data);
        }else{
            exception($result_upd['msg']);
        }
        
        Db::commit();
        $this->success('ok');
    }catch (\Exception $e){
        Db::rollback();
        $this->error($e->getMessage());
    }
}
```

#### 2.2 腾讯云VCLM服务调用
**服务类方法**：`Utils::createImageAnimateJob()`

```php
// application/service/Utils.php
public function createImageAnimateJob($params,$admin_id)
{
    $oem_status = $this->checkOem($admin_id);
    if(!$oem_status) {
        return $this->returnData(0,'当前被禁用');
    }
    
    $tx_config = $this->getOemConfig($admin_id,'tencent');
    if(!$tx_config) {
        return $this->returnData(0,'暂无配置');
    }
    
    try {
        // 创建腾讯云客户端
        $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
        $httpProfile = new HttpProfile();
        $httpProfile->setEndpoint("vclm.tencentcloudapi.com");
        
        $clientProfile = new ClientProfile();
        $clientProfile->setHttpProfile($httpProfile);
        
        $client = new VclmClient($cred, "ap-guangzhou", $clientProfile);
        
        // 创建请求对象
        $req = new SubmitImageAnimateJobRequest();
        $req->fromJsonString(json_encode($params));
        
        // 发送请求
        $resp = $client->SubmitImageAnimateJob($req);
        
        return $this->returnData(1,'ok',$resp->toJsonString());
    }catch (TencentCloudSDKException $e) {
        return $this->returnData(0,$e->getMessage());
    }
}
```

**执行流程**：
1. 验证OEM状态和配置
2. 获取腾讯云配置信息
3. 创建腾讯云VCLM客户端
4. 构造SubmitImageAnimateJobRequest请求
5. 调用SubmitImageAnimateJob接口
6. 返回任务ID和状态

### 3. 任务状态查询和结果处理

#### 3.1 定时任务查询视频状态
**定时任务**：通过命令行或定时脚本执行

```php
// application/index/controller/Index.php (定时任务调用)
public function checkVideoStatus()
{
    $tools_order_model = new Toolsorder();
    $utils = new Utils();
    
    // 查询处理中的订单
    $where = [
        'tools_rule' => 'ai_shipin',
        'status' => 1  // 处理中状态
    ];
    $tools_order_list = $tools_order_model->where($where)->select();
    
    foreach ($tools_order_list as $v) {
        $success_data = json_decode($v['success'], true);
        $params = [
            'JobId' => $success_data['JobId']
        ];
        
        // 查询腾讯云任务状态
        $result = $utils->getVideoStatus($params, $v['oem_id']);
        
        if($result['code'] == 1) {
            $data = json_decode($result['data'], true);
            
            if($data['Status'] == 'DONE') {
                // 任务完成，下载视频并上传到OSS
                $videoUrl = $data['ResultVideoUrl'];
                $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$v['users_id'].'.mp4';
                
                // 下载视频到本地
                file_put_contents('.'.$localTempFile, file_get_contents($videoUrl));
                
                // 上传到OSS
                $oss_result = $utils->uploadOss($localTempFile, $v['oem_id']);
                
                if($oss_result['code'] == 1) {
                    // 更新订单状态为完成
                    $success = [
                        'video' => $oss_result['data']['url'],
                    ];
                    $upd_data = [
                        'success' => json_encode($success),
                        'status' => 2,
                        'endtime' => time()
                    ];
                    $tools_order_model->where('id', $v['id'])->update($upd_data);
                    
                    // 删除本地临时文件
                    unlink('.'.$localTempFile);
                }
            }
        }
    }
}
```

#### 3.2 腾讯云状态查询服务
```php
// application/service/Utils.php
public function getVideoStatus($params,$admin_id)
{
    $oem_status = $this->checkOem($admin_id);
    if(!$oem_status) {
        return $this->returnData(0,'当前被禁用');
    }
    
    $tx_config = $this->getOemConfig($admin_id,'tencent');
    if(!$tx_config) {
        return $this->returnData(0,'暂无配置');
    }
    
    try {
        $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
        $httpProfile = new HttpProfile();
        $httpProfile->setEndpoint("vclm.tencentcloudapi.com");
        
        $clientProfile = new ClientProfile();
        $clientProfile->setHttpProfile($httpProfile);
        
        $client = new VclmClient($cred, "ap-guangzhou", $clientProfile);
        $req = new DescribeImageAnimateJobRequest();
        $req->fromJsonString(json_encode($params));
        
        $resp = $client->DescribeImageAnimateJob($req);
        
        return $this->returnData(1,'ok',$resp->toJsonString());
    }catch (TencentCloudSDKException $e) {
        return $this->returnData(0,$e->getMessage());
    }
}
```

### 4. 外部回调处理

#### 4.1 第三方服务回调接口
**接口**：`/api/video/submit_task`

```php
// application/api/controller/Video.php
public function submit_task()
{
    $params = $this->request->post();
    $tools_order_model = new Toolsorder();
    
    if (!empty($params['url']) && $params['status'] == 3) {
        // 成功回调
        $success = [
            'video' => $params['url'],
        ];
        $upd_data = [
            'success' => json_encode($success),
            'status' => 2,
            'endtime' => time()
        ];
        $tools_order_model->where('id',$params['id'])->update($upd_data);
    }else{
        // 失败回调
        $error = [
            'text' => '视频合成失败',
        ];
        $upd_data = [
            'error' => json_encode($error),
            'status' => 3,
            'endtime' => time()
        ];
        $tools_order_model->where('id',$params['id'])->update($upd_data);
    }
    
    $this->success('ok',['status' => 1],200);
}
```

### 5. 任务队列管理

#### 5.1 获取待处理任务
**接口**：`/api/video/get_tasks`

```php
// application/api/controller/Video.php
public function get_tasks()
{
    $oem_id = $this->request->param('oem_id');
    $power = $this->request->param('power');
    
    $tools_order_model = new Toolsorder();
    $where = [
        'oem_id' => $oem_id,
        'tools_rule' => 'ai_shipin',
        'status' => 1
    ];
    
    $tools_order_list = $tools_order_model->where($where)->limit(5)->order('id asc')->select();
    $tasks = [];
    $human_model = new Human();
    
    foreach ($tools_order_list as $k => $v) {
        if($v['tools_rule'] == 'ai_shipin') {
            $content = json_decode($v['content'],true);
            $content = json_decode($content['text'],true);
            
            $human_info = $human_model->where('id',$content['aiAvatarId'])->find();
            if($human_info) {
                $tasks[$k]['id'] = $v['id'];
                $tasks[$k]['asr_format_audio_url'] = $content['asr_format_audio_url'];
                $tasks[$k]['reference_audio_text'] = $content['reference_audio_text'];
                $tasks[$k]['audio_text'] = $content['text'];
                $tasks[$k]['extra'] = NULL;
                $tasks[$k]['video_url'] = $human_info['video'];
                $tasks[$k]['rule'] = 'ai_shipin';
            }
        }
    }
    
    $data = [
        'tasks' => $tasks,
        'backgrounds' => []
    ];
    $this->success('', $data, 200);
}
```

## 数据流转图

```mermaid
graph TD
    A[用户上传数字人视频] --> B[保存到fa_human表]
    C[用户提交合成任务] --> D[创建fa_toolsorder订单]
    D --> E[调用腾讯云VCLM服务]
    E --> F[获取任务ID]
    F --> G[定时查询任务状态]
    G --> H{任务是否完成?}
    H -->|是| I[下载视频到本地]
    H -->|否| G
    I --> J[上传视频到OSS]
    J --> K[更新订单状态为完成]
    K --> L[删除本地临时文件]
    
    M[外部回调] --> N[更新订单状态]
```

## 关键技术点

### 1. 腾讯云VCLM服务
- **服务名称**：Video Content and Live Media
- **主要接口**：
  - `SubmitImageAnimateJob`：提交图片跳舞任务
  - `DescribeImageAnimateJob`：查询任务状态
- **支持的动作模板**：ke3(科目三)、tuziwu(兔子舞)、huajiangwu(划桨舞)

### 2. 异步处理机制
- 任务提交后立即返回，不等待处理完成
- 通过定时任务轮询检查任务状态
- 支持外部回调通知机制

### 3. 文件存储策略
- 临时文件：本地存储用于中转
- 永久存储：上传到云存储（OSS/COS）
- 自动清理：处理完成后删除本地临时文件

### 4. 错误处理
- 权限验证：检查用户余额和权限
- 服务降级：腾讯云服务异常时的处理
- 事务回滚：确保数据一致性

## 性能优化

### 1. 批量处理
- 一次获取多个待处理任务
- 并发调用腾讯云接口

### 2. 缓存策略
- 配置信息缓存
- 任务状态缓存

### 3. 资源管理
- 及时清理临时文件
- 控制并发任务数量

---

**更新日期**：2025年1月28日  
**文档版本**：v1.0
