<?php

namespace app\admin\command;

use app\admin\model\AuthRule;
use app\common\model\Human;
use app\common\model\Toolsorder;
use app\service\Utils;
use ReflectionClass;
use ReflectionMethod;
use think\Cache;
use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Loader;

class Video extends Command
{
    protected $model = null;

    protected function configure()
    {
        $this
            ->setName('video')// 定义必需的参数
            ->addArgument('param1', Argument::REQUIRED, '这是第一个必需参数')
            ->setDescription('Build auth menu from controller');
        //要执行的controller必须一样，不适用模糊查询
    }

    protected function execute(Input $input, Output $output)
    {
        $admin_id = $input->getArgument('param1');
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_shipin',
            'success' => NULL,
            'oem_id' => $admin_id
        ];
//        $output->info($admin_id);
        $tools_order_list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        foreach ($tools_order_list as $k=>$v){

            $tools_order_model = new Toolsorder();

            $human_model = new Human();
            $utils = new Utils();
            $content = json_decode($v['content'],true);
            $content_arr = json_decode($content['text'],true);
            $human_info = $human_model->where('id',$content_arr['aiAvatarId'])->find();
            if(!empty($human_info)){
                //先生成音频
                $res = $utils->createAudio(['text' => $content_arr['text'],'FastVoiceType' => $content_arr['FastVoiceType']],$v['oem_id']);
                if($res['code'] == 1){
                    $success = [
                        'audio' => $res['data']['url'],
                        'video' => $human_info['video'],
                    ];
                    $upd_data = [
                        'success' => json_encode($success),
                    ];
                }else
                {
                    $error = [
                        'text' => "生成音频失败".json_encode($res),
                    ];
                    $upd_data = [
                        'error' => json_encode($error),
                        'status' => 3,
                        'endtime' => time(),
                    ];
                }
                $tools_order_model->where('id',$v['id'])->update($upd_data);
            }else
            {
                $error = [
                    'text' => '数字人形象不存在',
                ];
                $upd_data = [
                    'error' => json_encode($error),
                    'status' => 3,
                    'endtime' => time()
                ];
                $tools_order_model->where('id',$v['id'])->update($upd_data);
            }


        }
    }


}
