define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'auth/admin/index',
                    add_url: 'auth/admin/add',
                    edit_url: 'auth/admin/edit',
                    del_url: 'auth/admin/del',
                    multi_url: 'auth/admin/multi',
                }
            });

            var table = $("#table");

            //在表格内容渲染完成后回调的事件
            table.on('post-body.bs.table', function (e, json) {
                $("tbody tr[data-index]", this).each(function () {
                    if (parseInt($("td:eq(1)", this).text()) == Config.admin.id) {
                        $("input[type=checkbox]", this).prop("disabled", true);
                    }
                });
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: 'ID',operate: false},
                        {field: 'username', title: __('Username'),operate:'LIKE'},
                        {field: 'nickname', title: __('Nickname'),operate:'LIKE'},
                        {field: 'groups_text', title: __('Group'), operate:false, formatter: Table.api.formatter.label},
                        {field: 'email', title: __('Email'), operate:'LIKE', formatter: Table.api.formatter.label},
                        {field: 'url', title: __('Url'), operate: Config.show ? 'LIKE' : Config.show,visible:Config.show, formatter: Table.api.formatter.url},
                        {field: 'admin_type', title: __('Admin_type'), searchList: {"1":__('Admin_type 1'),"2":__('Admin_type 2'),"3":__('Admin_type 3')}, formatter: Table.api.formatter.normal},
                        {field: 'mobile', title: __('Mobile')},
                        {field: 'admin_nickname', title: __('Admin_id')},
                        {field: 'status', title: __("Status"), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'logintime', title: __('Login time'),operate:false, formatter: Table.api.formatter.datetime,  addclass: 'datetimerange', sortable: true},
                        {field: 'createtime', title: __('Create time'),operate:false, formatter: Table.api.formatter.datetime,  addclass: 'datetimerange', sortable: true},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: function (value, row, index) {
                                if(row.id == Config.admin.id){
                                    return '';
                                }
                                return Table.api.formatter.operate.call(this, value, row, index);
                            },
                            buttons: [
                                {
                                    name: 'detail',
                                    title: __('工具次数'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    text: __('工具次数'),
                                    url: 'auth/admin/agent_tools',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                }

                            ]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        edit: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        agent_tools: function () {
            Form.api.bindevent($("form[role=form]"));
        }
    };
    return Controller;
});
