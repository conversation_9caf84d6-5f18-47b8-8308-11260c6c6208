<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Admin;
use app\common\model\Oemconfig;
use app\common\model\Toolsorder;
use app\common\model\Users;
use app\common\model\Human;
use app\service\Inspect;
use app\service\Utils;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;
use fast\Random;
use Intervention\Image\ImageManager;
use think\Config;
use think\Validate;

/**
 * 会员接口
 */
class Video extends Api
{
    protected $noNeedLogin = ['login', 'wxLogin', 'get_tasks','get_tasks1', 'submit_task','save_yinse', 'save_human', 'save_audio', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';


    public function get_tasks()
    {
        $params = $this->request->param();
        $oem_id = $params['agent_code'];
        $tools_order_model = new Toolsorder();
        $where = [
            'oem_id' => $oem_id,
            'status' => 1,
            'tools_rule' => 'ai_shipin',
            'success' => array('neq','')
        ];
        $tools_order_list = $tools_order_model->where($where)->limit(5)->order('id asc')->select();
        $tasks = [];
        foreach ($tools_order_list as $k => $v) {
            $tasks[$k]['id'] = $v['id'];
            $tasks[$k]['extra'] = NULL;
            $success = json_decode($v['success'], true);
            $tasks[$k]['sound_url'] = $success['audio'];
            $tasks[$k]['video_url'] = $success['video'];
        }
//        dump($ali_config);
       $data = [
            'tasks' => $tasks,
           'backgrounds' => []
        ];
        $this->success('', $data,200);
    }
    
    public function get_tasks1($power = 1)
    {
        $params = $this->request->param();
        $oem_id = $params['agent_code'];
        $tools_order_model = new Toolsorder();
        $where = [
            'oem_id' => $oem_id,
            'status' => 1,
            'tools_rule' => array('in','ai_shipin,ai_yinpin,ai_yinse'),
            'power' => $power
            // 'success' => array('neq','')
        ];
        $tools_order_list = $tools_order_model->where($where)->limit(5)->order('id asc')->select();
        $tasks = [];
        $human_model = new Human();
        foreach ($tools_order_list as $k => $v) {
            if($v['tools_rule'] == 'ai_shipin')
            {
                $content = json_decode($v['content'],true);
                $content = json_decode($content['text'],true);
                // dump($content);
                // die();
                $human_info = $human_model->where('id',$content['aiAvatarId'])->find();
                if($human_info)
                {
                    $tasks[$k]['id'] = $v['id'];
                    $tasks[$k]['asr_format_audio_url'] = $content['asr_format_audio_url'];
                    $tasks[$k]['reference_audio_text'] = $content['reference_audio_text'];
                    $tasks[$k]['audio_text'] = $content['text'];
                    $tasks[$k]['extra'] = NULL;
                    $tasks[$k]['video_url'] = $human_info['video'];
                    $tasks[$k]['rule'] = 'ai_shipin';
                }else
                {
                    $error = [
                        'text' => '视频合成失败，数字人已删除',
                    ];
                    $upd_data = [
                        'error' => json_encode($error),
                        'status' => 3,
                        'endtime' => time()
                    ];
                    $tools_order_model->where('id',$v['id'])->update($upd_data);
                }
                
            }elseif($v['tools_rule'] == 'ai_yinpin')
            {
                $tasks[$k]['id'] = $v['id'];
                $content = json_decode($v['content'], true);
                $tasks[$k]['asr_format_audio_url'] = $content['asr_format_audio_url'];
                $tasks[$k]['reference_audio_text'] = $content['reference_audio_text'];
                $tasks[$k]['audio_text'] = $content['text'];
                $tasks[$k]['rule'] = 'ai_yinpin';
            }else
            {
                $tasks[$k]['id'] = $v['id'];
                $content = json_decode($v['content'], true);
                $tasks[$k]['audio_url'] = $content['audio'];
                $tasks[$k]['rule'] = 'ai_yinse';
            }
            
        }
        $human_model = new Human();
        $where_human = [
            'oem_id' => $oem_id,
            'status' => 0,
            'power' => $power
        ];
        $human_list = $human_model->where($where_human)->limit(5)->order('id asc')->select();
        $tasks_human = [];
        foreach ($human_list as $k => $v) {
            $tasks_human[$k]['id'] = $v['id'];
            $tasks_human[$k]['extra'] = NULL;
            $tasks_human[$k]['rule'] = 'ai_human';
            $tasks_human[$k]['video_url'] = $v['video'];
        }
//        dump($ali_config);
        $tasks = array_merge($tasks,$tasks_human);
       $data = [
            'tasks' => $tasks,
           'backgrounds' => []
        ];
        $this->success('', $data,200);
    }
    
    
    public function save_human()
    {
        $params = $this->request->post();
        $human_model = new Human();
        $upd_data = [
            'reference_audio_text' => $params['reference_audio_text'] ?? '',
            'asr_format_audio_url' => $params['asr_format_audio_url'] ?? '',
            'audio' => $params['audio'] ?? '',
            'status' => $params['status']
            ];
        $human_model->where('id',$params['id'])->update($upd_data);
        $this->success('ok',['status' => 1],200);
        
        // $users_id = $human_model->where('id',$params['id'])->value('users_id');
        // $users_model = new Users();
        // $users_info = $users_model->where('id',$users_id)->find();
        // $add_data = [
        //         'name' => '创建音色',
        //         'users_id' => $users_id,
        //         'tools_id' => 0,
        //         'paydata'  => 4,
        //         ''
        //     ];
    }
    
    public function save_audio()
    {
        $params = $this->request->post();
//        file_put_contents('canshu.txt',json_encode($params));
//        dump($params);
//        die();
        $tools_order_model = new Toolsorder();
        if (!empty($params['audio']) && $params['status'] == 2) {
                $success = [
                    'audio' => $params['audio'],
                ];
            $upd_data = [
                'success' => json_encode($success),
                'status' => 2,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }else
        {
            $error = [
                'text' => '音频合成失败',
            ];
            $upd_data = [
                'error' => json_encode($error),
                'status' => 3,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }
        $this->success('ok',['status' => 1],200);
    }
    
    public function save_yinse()
    {
        $params = $this->request->post();
//        file_put_contents('canshu.txt',json_encode($params));
//        dump($params);
//        die();
        $tools_order_model = new Toolsorder();
        if (!empty($params['audio']) && $params['status'] == 1) {
                $success = [
                    'audio' => $params['audio'],
                    'asr_format_audio_url' => $params['asr_format_audio_url'],
                    'reference_audio_text' => $params['reference_audio_text'],
                ];
            $upd_data = [
                'success' => json_encode($success),
                'status' => 2,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }else
        {
            $error = [
                'text' => '音频合成失败',
            ];
            $upd_data = [
                'error' => json_encode($error),
                'status' => 3,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }
        $this->success('ok',['status' => 1],200);
    }


    public function submit_task()
    {
        $params = $this->request->post();
//        file_put_contents('canshu.txt',json_encode($params));
//        dump($params);
//        die();
        $tools_order_model = new Toolsorder();
        if (!empty($params['url']) && $params['status'] == 3) {
                $success = [
                    'video' => $params['url'],
                ];
            $upd_data = [
                'success' => json_encode($success),
                'status' => 2,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }else
        {
            $error = [
                'text' => '视频合成失败',
            ];
            $upd_data = [
                'error' => json_encode($error),
                'status' => 3,
                'endtime' => time()
            ];
            $tools_order_model->where('id',$params['id'])->update($upd_data);
        }
        $this->success('ok',['status' => 1],200);
    }





}
