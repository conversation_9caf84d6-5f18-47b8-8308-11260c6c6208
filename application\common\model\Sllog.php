<?php

namespace app\common\model;

use think\Model;


class Sllog extends Model
{

    

    

    // 表名
    protected $name = 'sllog';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_data_text',
//        'dec_data_text'
    ];
    

    
    public function getTypeDataList()
    {
        return ['1' => __('Type_data 1'), '2' => __('Type_data 2')];
    }

//    public function getDecDataList()
//    {
//        return ['1' => __('Dec_data 1'), '2' => __('Dec_data 2')];
//    }


    public function getTypeDataTextAttr($value, $data)
    {
        $value = $value ?: ($data['type_data'] ?? '');
        $list = $this->getTypeDataList();
        return $list[$value] ?? '';
    }


//    public function getDecDataTextAttr($value, $data)
//    {
//        $value = $value ?: ($data['dec_data'] ?? '');
//        $list = $this->getDecDataList();
//        return $list[$value] ?? '';
//    }

    public static function addLog($users_info,$tools_info,$mark,$type = 2)
    {
        if($type == 1)
        {
            $after = $tools_info['price'] + $users_info['score'];
        }else
        {
            $after = $users_info['score'] - $tools_info['price'];
        }
        $add = [
            'users_id' => $users_info['id'],
            'oem_id' => $users_info['oem_id'],
            'agent_id' => $users_info['agent_id'],
            'price' => $tools_info['price'],
            'before' => $users_info['score'],
            'after' => $after,
            'mark' => $mark,
            'tools_id' => $tools_info['id'],
            'type_data' => $type
        ];
        Sllog::create($add);
    }




    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function agent()
    {
        return $this->belongsTo('Admin', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function tools()
    {
        return $this->belongsTo('Tools', 'tools_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function users()
    {
        return $this->belongsTo('Users', 'users_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
