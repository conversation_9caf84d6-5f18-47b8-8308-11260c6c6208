<?php

namespace app\common\model;

use think\Model;


class Toolsorder extends Model
{

    

    

    // 表名
    protected $name = 'toolsorder';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'paydata_text',
        'status_text',
        'endtime_text'
    ];
    

    
    public function getPaydataList()
    {
        return ['1' => __('Paydata 1'), '2' => __('Paydata 2'), '3' => __('Paydata 3'), '4' => __('Paydata 4')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }


    public function getPaydataTextAttr($value, $data)
    {
        $value = $value ?: ($data['paydata'] ?? '');
        $list = $this->getPaydataList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function getEndtimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['endtime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setEndtimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function users()
    {
        return $this->belongsTo('Users', 'users_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function agent()
    {
        return $this->belongsTo('Admin', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function tools()
    {
        return $this->belongsTo('Tools', 'tools_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
