define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            if(Config.show) {
                var delurl = 'commissions/del';
                var editurl = 'commissions/edit';
            }else
            {
                var delurl = '';
                var editurl = '';
            }
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'commissions/index' + location.search,
                    add_url: 'commissions/add',
                    edit_url: editurl,
                    del_url: delurl,
                    multi_url: 'commissions/multi',
                    import_url: 'commissions/import',
                    table: 'commissions',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'order_id', title: __('Order_id'), operate: false, table: table, class: 'autocontent', formatter: function(value, row, index) {
                                //value：intro字段的值
                                //row：当前行所有字段的数据
                                //index：当前行索引
                                //示例：
                                if(row.order_type == 1)
                                {
                                    return row.viporder.name;
                                }else
                                {
                                    return row.toolsorder.name;
                                }
                            }},
                        {field: 'order_type', title: __('Order_type'), searchList: {"1":__('Order_type 1'),"2":__('Order_type 2')}, formatter: Table.api.formatter.normal},
                        {field: 'users.nickname', title: __('Users_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'parent.nickname', title: __('Parent_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'income.nickname', title: __('Income_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'level', title: __('Level')},
                        {field: 'rate', title: __('Rate'), operate:false},
                        {field: 'raw_amount', title: __('Raw_amount'), operate:false},
                        {field: 'computed_amount', title: __('Computed_amount'), operate:false},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'endtime', title: __('Endtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'oem.nickname', title: __('Oem_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'agent.nickname', title: __('Agent_id'),operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
