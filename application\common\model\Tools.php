<?php

namespace app\common\model;

use think\Model;


class Tools extends Model
{

    

    

    // 表名
    protected $name = 'tools';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_data_text',
        'show_data_text',
        'status_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }

    
    public function getTypeDataList()
    {
        return ['1' => __('Type_data 1'), '2' => __('Type_data 2'), '3' => __('Type_data 3'), '4' => __('Type_data 4')];
    }

    public function getShowDataList()
    {
        return ['1' => __('Show_data 1'), '2' => __('Show_data 2')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getTypeDataTextAttr($value, $data)
    {
        $value = $value ?: ($data['type_data'] ?? '');
        $list = $this->getTypeDataList();
        return $list[$value] ?? '';
    }


    public function getShowDataTextAttr($value, $data)
    {
        $value = $value ?: ($data['show_data'] ?? '');
        $list = $this->getShowDataList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function pages()
    {
        return $this->belongsTo('Pages', 'pages_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
