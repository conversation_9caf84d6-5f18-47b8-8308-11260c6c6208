<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\OssUpload;
use app\common\library\WxPay;
use app\common\model\Banner;
use app\common\model\Commissions;
use app\common\model\Draw;
use app\common\model\Human;
use app\common\model\Oemconfig;
use app\common\model\Sllog;
use app\common\model\Tools;
use app\common\model\Toolsnum;
use app\common\model\Toolsorder;
use app\common\model\Users;
use app\common\model\Vip;
use app\common\model\Viporder;
use app\service\Utils;
use fast\Date;
use think\Db;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }

    /**
     * 获取支付信息
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createPay()
    {
        $wxpay = new WxPay($this->oemId);
        $vip_model = new Vip();
        $vip_id = $this->request->param('id');
        $vip_info = $vip_model->where('id',$vip_id)->where('admin_id',$this->oemId)->find();
        if(!$vip_info){
            $this->error('暂不支持支付');
        }
        $user_info = $this->auth->getUserinfo();
        $out_trade_no = generateOrderNumber();
        $add = [
            'name' => $vip_info['name'],
            'vip_id' => $vip_id,
            'users_id' => $user_info['id'],
            'price' => $vip_info['price'],
            'order_number' => $out_trade_no,
            'status' => 0,
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
        ];
        $vip_order_model = new Viporder();
        $res_vip = $vip_order_model->save($add);
        if(!$res_vip){
            $this->error('下单失败');
        }
        $res = $wxpay->createPayInfo($user_info['openid'],$vip_info['name'],$out_trade_no,$vip_info['price'] * 100);
        if(!$res){
            $this->error('支付失败');
        }
        $this->success('ok',$res);

    }

    /** 获取首页配置项
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getIndexConfig()
    {
        //获取首页背景图
        $index_bg = $this->oemConfig['xcx']['xcx_index_bj'];
        //获取首页背景颜色
        $index_bg_color = $this->oemConfig['xcx']['xcx_index_color'];
        //获取小程序名称
        $xcx_name = $this->oemConfig['xcx']['xcx_name'];


        //获取首页轮播图
        $banner_model = new Banner();
        $banner_where = [
            'admin_id' => $this->oemId,
            'show_data' => 1
        ];
        $banner_list = $banner_model->with(['pages'])->where($banner_where)->order('weigh','desc')->select();
        //获取首页顶部小图
        $top_banner_where = [
            'admin_id' => $this->oemId,
            'show_data' => 4
        ];
        $top_img = $banner_model->with(['pages'])->where($top_banner_where)->order('weigh','desc')->find();


            //获取工具列表
        $tools_model = new Tools();
        $tools_where = [
            'admin_id' => $this->oemId,
            'show_data' => 1,
            'status' => 1
        ];
        $tools_list = $tools_model->with(['pages'])->where($tools_where)->order('weigh','desc')->select();
        $data = [
            'index_config' => [
                'index_bg' => $index_bg,
                'index_bg_color' => $index_bg_color,
                'xcx_name' => $xcx_name,
                'top_img' => $top_img,
            ],
            'banner_list' => $banner_list,
            'tools_list' => $tools_list,
        ];
        $this->success('请求成功', $data);
    }

    /**
     * 获取AI绘画 AI写真记录
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getPhotoList()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_order_model = new Toolsorder();
        $where = [
            'tools_rule' => array('in','ai_huihua,ai_xiezhen,ai_gaoqing,ai_style'),
            'users_id' => $user_info['id'],
        ];
        $tools_order_list = $tools_order_model->where('oem_id',$user_info['oem_id'])->where($where)->order('id desc')->select();
        $data = [];
        foreach ($tools_order_list as $k=>$tools_order_info) {
            $data[$k]['image'] = '';
            if($tools_order_info['status'] == 2){
                $success_arr = json_decode($tools_order_info['success'],true);
                $data[$k]['image'] = $success_arr['image'];
            }
            $data[$k]['id'] = $tools_order_info['id'];
            $data[$k]['status'] = $tools_order_info['status'];
            $data[$k]['title'] = $tools_order_info['name'];
            $data[$k]['createtime'] = date('Y-m-d',$tools_order_info['endtime']);

        }
        $this->success('ok',$data);
    }

    /**
     * 获取视频列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getVideoList()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_order_model = new Toolsorder();
        $where = [
            'tools_rule' => array('in','ai_tiaowu,ai_shipin')
        ];
        $tools_order_list = $tools_order_model->where('oem_id',$user_info['oem_id'])->where('users_id',$user_info['id'])->where($where)->order('id desc')->select();
        $data = [];
        foreach ($tools_order_list as $k=>$tools_order_info) {
            $data[$k]['video'] = '';
            if($tools_order_info['status'] == 2){
                $success_arr = json_decode($tools_order_info['success'],true);
                $data[$k]['video'] = $success_arr['video'];
            }
            $data[$k]['id'] = $tools_order_info['id'];
            $data[$k]['status'] = $tools_order_info['status'];
            if($tools_order_info['tools_rule'] == 'ai_shipin')
            {
                $content = json_decode($tools_order_info['content'],true);
                $content_arr = json_decode($content['text'],true);
                $data[$k]['title'] = $content_arr['title'];
            }else
            {
                $data[$k]['title'] = $tools_order_info['name'];
            }
            $data[$k]['createtime'] = date('Y-m-d',$tools_order_info['createtime']);
        }
        $this->success('ok',$data);
    }

    /**
     * 获取算力日志
     * @return void
     * @throws \think\exception\DbException
     */
    public function getScoreLogList()
    {
        $user_info = $this->auth->getUserInfo();
        $sllog_model = new Sllog();
        $type = $this->request->post('type') ?? 1;
        $sl_log_list = $sllog_model->where('type_data',$type)->where('users_id',$user_info['id'])->order('id desc')->paginate(10);
        $sl_list = $sl_log_list->items();
        foreach ($sl_log_list as $k=>$sl_log_info) {
            $sl_log_list[$k]['createtime'] = date('Y-m-d',$sl_log_info['createtime']);
        }
        $data = [
            'list' => $sl_log_list->items(),
            'total' => $sl_log_list->total(),
        ];
        $this->success('ok',$data);
    }


    /**
     * 获取我的团队
     * @return void
     * @throws \think\exception\DbException
     */
    public function getMyTeamList()
    {
        $user_info = $this->auth->getUserInfo();
        $users_model = new Users();
        $type = $this->request->post('type') ?? 1;
        $where = [
            'oem_id' => $this->oemId,
        ];
        if($type == 1)
        {
            $where['parent_id'] = $user_info['id'];
        }else
        {
            $where['j_parent_id'] = $user_info['id'];
        }
        $my_team_list = $users_model->where($where)->order('id desc')->paginate(10);
//        dump($where);
        $team_list = $my_team_list->items();
        foreach ($team_list as $k=>$info) {
            $team_list[$k]['createtime'] = date('Y-m-d',$info['createtime']);
        }
        $data = [
            'list' => $team_list,
            'total' => $my_team_list->total(),
        ];
        $this->success('ok',$data);
    }

    /**
     * 获取分销中心信息
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getDistribution()
    {
        $user_info = $this->auth->getUserInfo();
        $oem_config_model = new Oemconfig();
        $commissions_model = new Commissions();
        $commissions_where = [
            'income_id' => $user_info['id'],
            'status' => 2,
            'oem_id' => $user_info['oem_id']
        ];
        //总共收益
        $total_computed_amount = $commissions_model->where($commissions_where)->sum('computed_amount');
        //本月收益
        $start_time_yue = Date::unixtime('month');
        $end_time = time();
        $commissions_where['createtime'] = array('between',[$start_time_yue,$end_time]);
        $month_computed_amount = $commissions_model->where($commissions_where)->sum('computed_amount');
        //今日收益
        $start_time = Date::unixtime('day');
        $end_time = time();
        $commissions_where['createtime'] = array('between',[$start_time,$end_time]);
        $day_computed_amount = $commissions_model->where($commissions_where)->sum('computed_amount');

        //团队人数
        $users_model = new Users();
        //第一级
        $users_count = $users_model->where('parent_id',$user_info['id'])->count();
        //第二级
        $j_users_count = $users_model->where('j_parent_id',$user_info['id'])->count();
        $team_count = $users_count + $j_users_count;
        //本月新增
        $users_where_yue = [
            'createtime' => array('between',[$start_time_yue,$end_time])
        ];
        //第一级
        $users_count_yue = $users_model->where($users_where_yue)->where('parent_id',$user_info['id'])->count();
        //第二级
        $j_users_count_yue = $users_model->where($users_where_yue)->where('j_parent_id',$user_info['id'])->count();
        $team_count_yue = $users_count_yue + $j_users_count_yue;
        //今日新增
        $users_where = [
            'createtime' => array('between',[$start_time,$end_time])
        ];
        //第一级
        $users_count_day = $users_model->where($users_where)->where('parent_id',$user_info['id'])->count();
        //第二级
        $j_users_count_day = $users_model->where($users_where)->where('j_parent_id',$user_info['id'])->count();
        $team_count_day = $users_count_day + $j_users_count_day;

        //上级名称
        $parent_name = $users_model->where('id',$user_info['parent_id'])->value('nickname');
        //分销说明
        $utils = new Utils();
        $rebate_config = $utils->getOemConfig($this->oemId,'rebate');

        $data = [
            'users' => [
                'nickname' => $user_info['nickname'],
                'parent_name' => $parent_name,
                'money' => $user_info['money'],
                'total_computed_amount' => $total_computed_amount,
                'total_computed_amount_yue' => $month_computed_amount,
                'total_computed_amount_day' => $day_computed_amount,
                'qrcode' => $user_info['qrcode'],
            ],
            'team' => [
                'team_count' => $team_count,
                'team_count_yue' => $team_count_yue,
                'team_count_day' => $team_count_day,
            ],
            'rebate_content' => $rebate_config['rebate_content'],
            'rebate_commission' => $rebate_config['commission'],
            'rebate_min_cash' => $rebate_config['min_cash'],
        ];
        $this->success('ok',$data);


    }

    /**
     * 申请提现
     * @return void
     */
    public function createDraw()
    {
        $params = $this->request->post();
        $user_info = $this->auth->getUserInfo();
        $draw_model = new Draw();
        if($user_info['money'] < $params['money']){
            $this->error('余额不足');
        }

        $utils = new Utils();
        $rebate_config = $utils->getOemConfig($this->oemId,'rebate');
        Db::startTrans();
        try {
            $after = $user_info['money'] - $params['money'];
            $commission_actual = $params['money'] - round($params['money'] * $rebate_config['commission'] / 100,2);
            $add = [
                'users_id' => $user_info['id'],
                'money' => $params['money'],
                'before' => $user_info['money'],
                'after' => $after,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'status' => 1,
                'number' => generateOrderNumber(),
                'commission' => $rebate_config['commission'],
                'commission_actual' => $commission_actual,
            ];
            $res = $draw_model->save($add);
            $users_model = new Users();
            $users_model->where('id',$user_info['id'])->setDec('money',$params['money']);
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok');
    }

    /**
     * 获取提现记录
     * @return void
     * @throws \think\exception\DbException
     */
    public function getDrawList()
    {
        $user_info = $this->auth->getUserInfo();
        $draw_model = new Draw();
        $status = $this->request->post('status') ?? 1;
        $where = [
            'users_id' => $user_info['id'],
            'oem_id' => $this->oemId
        ];
        if($status)
        {
            $where['status'] = $status;
        }
        $oem_config_model = new Oemconfig();
        $payConfig = $oem_config_model->where('group','in','wxpay,xcx')->where('admin_id',$this->oemId)->select();
        $wx_config = [];
        if(!empty($payConfig)){
            foreach ($payConfig as $pay){
                $wx_config[$pay['name']] = $pay['value'];
            }
        }
        $draw_list = $draw_model->where($where)->order('id desc')->paginate(10);
        $draw = $draw_list->items();
        foreach ($draw_list as $k=>$draw_info) {
            $draw_list[$k]['createtime'] = date('Y-m-d',$draw_info['createtime']);
            $draw_list[$k]['appid'] = $wx_config['xcx_appid'];
            $draw_list[$k]['mchid'] = $wx_config['wx_mchid'];
        }
        $data = [
            'list' => $draw_list->items(),
            'total' => $draw_list->total(),
        ];
        $this->success('ok',$data);
    }
    
    public function updateDraw()
    {
        $user_info = $this->auth->getUserInfo();
        $draw_model = new Draw();
        $status = $this->request->post('status') ?? '';
        $id = $this->request->post('id') ?? '';
        if($id == '' || $status == '')
        {
            $this->error('参数错误');
        }
        $where = [
            'users_id' => $user_info['id'],
            'oem_id' => $this->oemId,
            'id' => $id
        ];
        $upd_data = [
            'status' => $status
            ];
        
        $res = $draw_model->where($where)->update($upd_data);
        if($res)
        {
            $this->success('已确认收款');
        }else
        {
            $this->error('失败');
        }
    }

    /**
     *
     * 获取分佣记录
     * @return void
     * @throws \think\exception\DbException
     */
    public function getCommissionList()
    {
        $user_info = $this->auth->getUserInfo();
        $commissions_model = new Commissions();
        $type = $this->request->post('type');
        $where = [
            'income_id' => $user_info['id'],
            'commissions.oem_id' => $this->oemId
        ];
        if($type)
        {
            $where['level'] = $type;
        }
//        dump($where);

        $commissions_list = $commissions_model->with(['users'])->where($where)->order('id desc')->paginate(10);
        $commissions = $commissions_list->items();
        foreach ($commissions_list as $k=>$commissions_info) {
            $commissions_list[$k]['createtime'] = date('Y-m-d',$commissions_info['createtime']);

            $commissions_info->getRelation('users')->visible(['nickname']);
        }
        $data = [
            'list' => $commissions_list->items(),
            'total' => $commissions_list->total(),
        ];
        $this->success('ok',$data);
    }

    /**
     * 获取贴牌用户系统配置
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function indexkfSet()
    {
        $utils = new Utils();
        $sys_config = $utils->getOemConfig($this->oemId,'system');
        $this->success('ok',$sys_config);
    }


    /**
     * 获取工具需要消耗的算力
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getToolsConfig()
    {
        $user_info = $this->auth->getUserInfo();
        $rule = $this->request->param('rule');
        if(empty($rule)){
            $this->error('错误配置失败');
        }
        $tools_model = new Tools();
        $tools_where = [
            'admin_id' => $this->oemId,
            'rule' => $rule,

        ];
        $tools_info = $tools_model->where($tools_where)->find();
        if(empty($tools_info)){
            $this->error('获取配置失败');
        }
        $data = [
            'aiText' => $tools_info['price'],
        ];
        $this->success('', $data);
    }

    /**
     * 删除AI记录
     * @return void
     */
    public function delToolsOrder()
    {
        $user_info = $this->auth->getUserInfo();
        $param = $this->request->post();
        $tools_order_model = new Toolsorder();
        $tools_where = [
            'id' => $param['id'],
            'oem_id' => $this->oemId,
            'users_id' => $user_info['id']
        ];
        $result = $tools_order_model->where($tools_where)->delete();
        if($result){
            $this->success('删除成功');
        }else
        {
            $this->error('删除失败');
        }
    }

    /**
     * 获取会员套餐列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getVipList()
    {
        $user_info = $this->auth->getUserInfo();
        $vip_model = new Vip();
        $tools_model = new Tools();
        $tools_num_model = new Toolsnum();
        $vip_list = $vip_model->where('admin_id',$this->oemId)->where('status' , 1)->order('weigh desc')->select();
        $data = [];
        foreach ($vip_list as $k=>$vip_info) {
            $tools_num_list = $tools_num_model->where('vip_id',$vip_info['id'])->select();
            $vip_tools = [];
            if($tools_num_list)
            {
                foreach ($tools_num_list as $k1=>$tools_info) {
                    $tools_name = $tools_model->where('id',$tools_info['tools_id'])->value('name');
                    $vip_tools[$k1] = $tools_name.':'.$tools_info['num'].'次';
                }
            }
            $data[$k]['name'] = $vip_info['name'];
            $data[$k]['id'] = $vip_info['id'];
            $data[$k]['price'] = $vip_info['price'];
            $data[$k]['old_price'] = $vip_info['old_price'];
            $data[$k]['days'] = $vip_info['days'];
            $data[$k]['score'] = $vip_info['score'];
            $data[$k]['content'] = $vip_info['content'];
            $data[$k]['tools_list'] = $vip_tools;
        }
        if($this->oemId == 28)
        {
            // $data = [];
        }
        $this->success('ok',$data);
    }

    public function getXcxUserInfo()
    {
        $user_info = $this->auth->getUserInfo();
        $user_info['viptime'] = date('Y-m-d',$user_info['viptime']);
        $tools_num_model = new Toolsnum();
        $tools_model = new Tools();
        $tools_list = $tools_model->where('admin_id',$this->oemId)->where('status' , 1)->order('weigh desc')->limit(4)->select();
        foreach ($tools_list as $k=>$tools_info) {
            $num = $tools_num_model->where('tools_id',$tools_info['id'])->where('users_id',$user_info['id'])->value('num');
            $user_info['tools'][$k] = [
                'name' => $tools_info['name'],
                'num' => $num,
            ];
            
            $user_info['get_phone'] = $this->oemConfig['xcx']['get_phone'];
        }
        $this->success('ok',$user_info);
    }


    /**
     * 添加数字人形象
     * @return void
     */
    public function createHuman()
    {
        $params = $this->request->param();
        $user_info = $this->auth->getUserInfo();
        if($params['video'] && $params['title']){
            $add = [
                'users_id' => $user_info['id'],
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'title' => $params['title'],
                'video' => $params['video'],
                'cover' => $params['video'].'?x-oss-process=video/snapshot,t_1,m_fast',
                'power' => $user_info['power']
            ];
            $human_model = new Human();
            $res = $human_model->save($add);
            if($res){
                $this->success('添加成功');
            }else
            {
                $this->error('添加失败');
            }
        }else
        {
            $this->error('参数不完整');
        }
    }

    /**
     * 获取视频和音色列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getMediaList()
    {
        $human_model = new Human();
        $user_info = $this->auth->getUserInfo();
        $human_list = $human_model->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->order('id desc')->select();

        foreach ($human_list as $k=>$human_info) {
            $human_list[$k]['createtime'] = date('Y-m-d',$human_info['createtime']);
        }
        $tools_order_model = new Toolsorder();
        $audio_list = $tools_order_model->where('tools_rule','ai_yinse')->where('status' , 2)->where('oem_id',$this->oemId)->order('id desc')->select();
        $audio_data = [];
        foreach ($audio_list as $k=>$audio_info) {
            $content = json_decode($audio_info['content'],true);
            $success = json_decode($audio_info['success'],true);
            $FastVoiceType_arr = explode('=',$success['text']);
//            $audio_arr = explode('=',$success['audio']);
//            $FastVoiceType = $FastVoiceType_arr[1];
            $audio_data[$k]['title'] = $content['title'];
            $audio_data[$k]['audioUrl'] = $success['audio'] ?? '';
            $audio_data[$k]['FastVoiceType'] = $FastVoiceType_arr[1];
            $audio_data[$k]['id'] = $audio_info['id'];
            $audio_data[$k]['isAuthor'] = 1;
            $audio_data[$k]['isPlay'] = 1;
            $audio_data[$k]['isEnd'] = 0;
        }
        $data = [
            'video' => $human_list,
            'audio' => $audio_data,
        ];
        $this->success('ok',$data);
    }
    
    public function delHuman()
    {
        $human_model = new Human();
        $user_info = $this->auth->getUserInfo();
        $id = $this->request->post('id');
        $res = $human_model->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->where('id',$id)->delete();
        if($res)
        {
            $this->success('ok'); 
        }else
        {
            $this->error('删除失败');
        }
    }
    
    
    public function getNewMediaList()
    {
        $human_model = new Human();
        $user_info = $this->auth->getUserInfo();
        $human_list = $human_model->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->order('id desc')->select();

        $audio_data = [];
        foreach ($human_list as $k=>$human_info) {
            $human_list[$k]['createtime'] = date('Y-m-d',$human_info['createtime']);
            $audio_data[$k]['title'] = $human_info['title'];
            $audio_data[$k]['audioUrl'] = $human_info['audio'] ?? '';
            $audio_data[$k]['asr_format_audio_url'] = $human_info['asr_format_audio_url'];
            $audio_data[$k]['reference_audio_text'] = $human_info['reference_audio_text'];
            $audio_data[$k]['id'] = $human_info['id'];
            $audio_data[$k]['isAuthor'] = 1;
            $audio_data[$k]['isPlay'] = 1;
            $audio_data[$k]['isEnd'] = 0;
            $audio_data[$k]['status'] = $human_info['status'];
        }
        $data = [
            'video' => $human_list,
            'audio' => $audio_data,
        ];
        $this->success('ok',$data);
    }
    
    public function getMediaList_v2()
    {
        $human_model = new Human();
        $user_info = $this->auth->getUserInfo();
        $human_list = $human_model->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->order('id desc')->select();

        foreach ($human_list as $k=>$human_info) {
            $human_list[$k]['createtime'] = date('Y-m-d',$human_info['createtime']);
        }
        $tools_order_model = new Toolsorder();
        $audio_list = $tools_order_model->where('tools_rule','ai_yinse')->where('status' , 2)->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->order('id desc')->select();
        $audio_data = [];
        foreach ($audio_list as $k=>$audio_info) {
            $content = json_decode($audio_info['content'],true);
            $success = json_decode($audio_info['success'],true);
            // $FastVoiceType_arr = explode('=',$success['text']);
//            $audio_arr = explode('=',$success['audio']);
//            $FastVoiceType = $FastVoiceType_arr[1];
            if(isset($success['asr_format_audio_url']))
            {
                $audio_data[$k]['title'] = $content['title'];
                $audio_data[$k]['audioUrl'] = $success['audio'] ?? '';
                $audio_data[$k]['asr_format_audio_url'] = $success['asr_format_audio_url'];
                $audio_data[$k]['reference_audio_text'] = $success['reference_audio_text'];
                $audio_data[$k]['id'] = $audio_info['id'];
                $audio_data[$k]['isAuthor'] = 1;
                $audio_data[$k]['isPlay'] = 1;
                $audio_data[$k]['isEnd'] = 0;
                $audio_data[$k]['status'] = $audio_info['status'];
            }
            
        }
        $data = [
            'video' => $human_list,
            'audio' => $audio_data,
        ];
        $this->success('ok',$data);
    }


    public function getVoiceGeneLogList()
    {
        $user_info = $this->auth->getUserinfo();
        $tools_order_model = new Toolsorder();
        $tools_order_list = $tools_order_model->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->where('tools_rule','ai_yinpin')->order('id desc')->select();
        $data = [];
        foreach ($tools_order_list as $k=>$tools_order_info) {
            $success = json_decode($tools_order_info['success'],true);
            $content = json_decode($tools_order_info['content'],true);
            $data[$k]['title'] = mb_substr($content['text'], 0, 10, 'UTF-8');
            $data[$k]['audio'] = $success['audio'] ?? '';
            $data[$k]['id'] = $tools_order_info['id'];
            $data[$k]['status'] = $tools_order_info['status'];
            $data[$k]['createtime'] = date('Y-m-d',$tools_order_info['createtime']);
        }
        $this->success('ok',$data);
    }



    public function uploadOss()
    {

        $file = $this->request->file('file');
//        dump($file);
        try {
            $upload = new OssUpload($file);
            $attachment = $upload->upload('',$this->oemId);
//                $utils = new Utils();
//                $result = $utils->uploadOss($attachment->url,$this->auth->id);
//                if($result['code'] == 1)
//                {
//                    //oss上传成功
//                    $attachment->url = $result['data']['url'];
//                }else
//                {
//                    $this->error($result['msg']);
//                }
            $this->success('ok', $attachment->url);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
//        dump($file);
    }

}
