<?php

namespace app\common\model;

use think\Model;


class Commissions extends Model
{

    

    

    // 表名
    protected $name = 'commissions';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'order_type_text',
        'status_text',
        'endtime_text'
    ];
    

    
    public function getOrderTypeList()
    {
        return ['1' => __('Order_type 1'), '2' => __('Order_type 2')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }


    public function getOrderTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['order_type'] ?? '');
        $list = $this->getOrderTypeList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function getEndtimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['endtime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setEndtimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    // 定义获取器自动判断关联类型
    public function viporder() {
        return $this->belongsTo('viporder', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function toolsorder() {
        return $this->belongsTo('toolsorder', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function agent()
    {
        return $this->belongsTo('Admin', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function users()
    {
        return $this->belongsTo('Users', 'users_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function parent()
    {
        return $this->belongsTo('Users', 'parent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function income()
    {
        return $this->belongsTo('Users', 'income_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
