<?php

namespace app\admin\controller\auth;

use app\admin\model\AuthGroup;
use app\admin\model\AuthGroupAccess;
use app\common\controller\Backend;
use app\common\library\WxPay;
use app\common\model\Config;
use app\common\model\Oemconfig;
use app\common\model\Tools;
use app\common\model\Toolsnum;
use app\common\model\Vip;
use fast\Random;
use fast\Tree;
use think\Db;
use think\Validate;

/**
 * 管理员管理
 *
 * @icon   fa fa-users
 * @remark 一个管理员可以有多个角色组,左侧的菜单根据管理员所拥有的权限进行生成
 */
class Admin extends Backend
{

    /**
     * @var \app\admin\model\Admin
     */
    protected $model = null;
    protected $selectpageFields = 'id,username,nickname,avatar';
    protected $searchFields = 'id,username,nickname';
    protected $childrenGroupIds = [];
    protected $childrenAdminIds = [];
    protected $dataLimit = 'auth'; //默认基类中为false，表示不启用，可额外使用auth和personal两个值
    protected $dataLimitField = 'admin_id'; //数据关联字段,当前控制器对应的模型表中必须存在该字段

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('Admin');

        //获取管理员类型
        $this->view->assign("adminTypeList", $this->model->getAdminTypeList());
        $this->childrenAdminIds = $this->auth->getChildrenAdminIds($this->auth->isSuperAdmin());
        $this->childrenGroupIds = $this->auth->getChildrenGroupIds($this->auth->isSuperAdmin());

        $groupList = collection(AuthGroup::where('id', 'in', $this->childrenGroupIds)->select())->toArray();

        Tree::instance()->init($groupList);
        $groupdata = [];
        if ($this->auth->isSuperAdmin()) {
            $result = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0));
            foreach ($result as $k => $v) {
                $groupdata[$v['id']] = $v['name'];
            }
        } else {
            $result = [];
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $childlist = Tree::instance()->getTreeList(Tree::instance()->getTreeArray($n['id']));
                $temp = [];
                foreach ($childlist as $k => $v) {
                    $temp[$v['id']] = $v['name'];
                }
                $result[__($n['name'])] = $temp;
            }
            $groupdata = $result;
        }

        $this->view->assign('groupdata', $groupdata);
        $this->assignconfig("admin", ['id' => $this->auth->id]);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $childrenGroupIds = $this->childrenGroupIds;
            $groupName = AuthGroup::where('id', 'in', $childrenGroupIds)
                ->column('id,name');
            $authGroupList = AuthGroupAccess::where('group_id', 'in', $childrenGroupIds)
                ->field('uid,group_id')
                ->select();

            $adminGroupName = [];
            foreach ($authGroupList as $k => $v) {
                if (isset($groupName[$v['group_id']])) {
                    $adminGroupName[$v['uid']][$v['group_id']] = $groupName[$v['group_id']];
                }
            }
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $adminGroupName[$this->auth->id][$n['id']] = $n['name'];
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $oem_where = [];
            if ($this->dataLimit && $this->dataLimitFieldAutoFill && $this->auth->id != 1) {
                $oem_where[$this->dataLimitField] = $this->auth->id;
            }
            $list = $this->model
                ->where($where)
                ->where($oem_where)
                ->where('id', 'in', $this->childrenAdminIds)
                ->field(['password', 'salt', 'token'], true)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $k => &$v) {
                $v['admin_nickname'] = $this->model->where('id', $v['admin_id'])->value('nickname');
                $groups = isset($adminGroupName[$v['id']]) ? $adminGroupName[$v['id']] : [];
                $v['groups'] = implode(',', array_keys($groups));
                $v['groups_text'] = implode(',', array_values($groups));
            }
            unset($v);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");
            if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                $params[$this->dataLimitField] = $this->auth->id;
            }
            if ($params) {
                Db::startTrans();
                try {
                    if (!Validate::is($params['password'], '\S{6,30}')) {
                        exception(__("Please input correct password"));
                    }
                    $params['salt'] = Random::alnum();
                    $params['password'] = $this->auth->getEncryptPassword($params['password'], $params['salt']);
                    $params['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
                    $params['url'] = $params['url'] ?? $_SERVER['HTTP_HOST'];
                    
                    $result = $this->model->validate('Admin.add')->save($params);
                    if ($result === false) {
                        exception($this->model->getError());
                    }
                    $new_id = $this->model->id;
                    $params['admin_type'] = isset($params['admin_type']) ? $params['admin_type'] : 3; //默认开通的是代理
                    if($params['admin_type'] == '3')
                    {
                        
                        //代理，自动新增工具次数
                        $tools_model = new Tools();
                        $tools_list = $tools_model->where('admin_id',$this->auth->id)->select();
                        $toolsnum_model = new Toolsnum();
                        if(!empty($tools_list))
                        {
                            foreach ($tools_list as $k => $v) {
                                $add_data[$k] = [
                                    'admin_id' => $new_id,
                                    'tools_id' => $v['id'],
                                    'num' => $v['num'],
                                ];
                            }
                            if(!empty($add_data))
                            {
                                $ret = $toolsnum_model->saveAll($add_data);
                                if($ret === false)
                                {
                                    exception($toolsnum_model->getError());
                                }
                            }
                        }
                        $wxPay = new WxPay($this->auth->id);
                        $wxPay->getXcxQrcode($new_id);
                    }
                    if($params['admin_type'] == '2')
                    {
                        //OEM，自动新增配置信息和工具、vip信息
                        $config_model = new Config();
                        $config_list = $config_model->where('group','oemconfig')->select();
                        if(!empty($config_list))
                        {
                            foreach ($config_list as $k => $v) {
                                $config_data[$k] = [
                                    'name' => $v['name'],
                                    'title' => $v['title'],
                                    'tip' => $v['tip'],
                                    'type' => $v['type'],
                                    'group' => $v['value'],
                                    'admin_id' => $new_id,
                                ];
                            }
                            if(!empty($config_data))
                            {
                                $oem_config_model = new Oemconfig();
                                $res = $oem_config_model->saveAll($config_data);
                                if($res === false)
                                {
                                    exception($oem_config_model->getError());
                                }
                            }
                        }
                        $tools_model = new Tools();
                        $tools_list = $tools_model->where('admin_id',3)->select();
                        if(!empty($tools_list))
                        {
                            foreach ($tools_list as $k => $v) {
                                $tools_data[$k] = [
                                    'name' => $v['name'],
                                    'desc' => $v['desc'],
                                    'rule' => $v['rule'],
                                    'image' => $v['image'],
                                    'type_data' => $v['type_data'],
                                    'price' => $v['price'],
                                    'pages_id' => $v['pages_id'],
                                    'show_data' => $v['show_data'],
                                    'status' => $v['status'],
                                    'weigh' => $v['weigh'],
                                    'admin_id' => $new_id,
                                ];
                            }
                            if(!empty($tools_data))
                            {
                                $res = $tools_model->saveAll($tools_data);
                                if($res === false)
                                {
                                    exception($tools_model->getError());
                                }
                            }
                        }

                        $vip_model = new Vip();
                        $vip_list = $vip_model->where('admin_id',3)->select();
                        if(!empty($vip_list))
                        {
                            foreach ($vip_list as $k => $v) {
                                $vip_add[$k] = [
                                    'name' => $v['name'],
                                    'price' => $v['price'],
                                    'old_price' => $v['old_price'],
                                    'days' => $v['days'],
                                    'score' => $v['score'],
                                    'content' => $v['content'],
                                    'weigh' => $v['weigh'],
                                    'admin_id' => $new_id,
                                ];
                            }
                            if(!empty($vip_add))
                            {
                                $res = $vip_model->saveAll($vip_add);
                                if($res === false)
                                {
                                    exception($vip_model->getError());
                                }
                            }
                        }
                    }
                    $group = $this->request->post("group/a");

                    //过滤不允许的组别,避免越权
                    $group = array_intersect($this->childrenGroupIds, $group);
                    if (!$group) {
                        exception(__('The parent group exceeds permission limit'));
                    }

                    $dataset = [];
                    foreach ($group as $value) {
                        $dataset[] = ['uid' => $this->model->id, 'group_id' => $value];
                    }
                    model('AuthGroupAccess')->saveAll($dataset);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign('admin_id',$this->auth->id);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (!in_array($row->id, $this->childrenAdminIds)) {
            $this->error(__('You have no permission'));
        }
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");
            if ($params) {
                Db::startTrans();
                try {
                    if ($params['password']) {
                        if (!Validate::is($params['password'], '\S{6,30}')) {
                            exception(__("Please input correct password"));
                        }
                        $params['salt'] = Random::alnum();
                        $params['password'] = $this->auth->getEncryptPassword($params['password'], $params['salt']);
                    } else {
                        unset($params['password'], $params['salt']);
                    }
                    //这里需要针对username和email做唯一验证
                    $adminValidate = \think\Loader::validate('Admin');
                    $adminValidate->rule([
                        'username' => 'require|regex:\w{3,30}|unique:admin,username,' . $row->id,
                        'email'    => 'require|email|unique:admin,email,' . $row->id,
                        'mobile'   => 'regex:1[3-9]\d{9}|unique:admin,mobile,' . $row->id,
//                        'url'   => 'unique:admin,url,' . $row->id,
                        'password' => 'regex:\S{32}',
                    ]);
                    $result = $row->validate('Admin.edit')->save($params);
                    if ($result === false) {
                        exception($row->getError());
                    }

                    // 先移除所有权限
                    model('AuthGroupAccess')->where('uid', $row->id)->delete();

                    $group = $this->request->post("group/a");

                    // 过滤不允许的组别,避免越权
                    $group = array_intersect($this->childrenGroupIds, $group);
                    if (!$group) {
                        exception(__('The parent group exceeds permission limit'));
                    }

                    $dataset = [];
                    foreach ($group as $value) {
                        $dataset[] = ['uid' => $row->id, 'group_id' => $value];
                    }
                    model('AuthGroupAccess')->saveAll($dataset);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $grouplist = $this->auth->getGroups($row['id']);
        $groupids = [];
        foreach ($grouplist as $k => $v) {
            $groupids[] = $v['id'];
        }
        $this->view->assign('admin_id',$this->auth->id);
        $this->view->assign("row", $row);
        $this->view->assign("groupids", $groupids);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $ids = array_intersect($this->childrenAdminIds, array_filter(explode(',', $ids)));
            // 避免越权删除管理员
            $childrenGroupIds = $this->childrenGroupIds;
            $adminList = $this->model->where('id', 'in', $ids)->where('id', 'in', function ($query) use ($childrenGroupIds) {
                $query->name('auth_group_access')->where('group_id', 'in', $childrenGroupIds)->field('uid');
            })->select();
            if ($adminList) {
                $deleteIds = [];
                foreach ($adminList as $k => $v) {
                    $deleteIds[] = $v->id;
                }
                $deleteIds = array_values(array_diff($deleteIds, [$this->auth->id]));
                if ($deleteIds) {
                    Db::startTrans();
                    try {
                        $this->model->destroy($deleteIds);
                        model('AuthGroupAccess')->where('uid', 'in', $deleteIds)->delete();
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                    $this->success();
                }
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('You have no permission'));
    }

    /**
     * 批量更新
     * @internal
     */
    public function multi($ids = "")
    {
        // 管理员禁止批量操作
        $this->error();
    }

    /**
     * 下拉搜索
     */
    public function selectpage()
    {
        $this->dataLimit = 'auth';
        $this->dataLimitField = 'admin_id';
        return parent::selectpage();
    }

    public function agent_tools($ids)
    {
        $row = $this->model->get(['id' => $ids]);

        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $agent_info = $this->model->where('id',$ids)->find();
        if (!$agent_info) {
            $this->error(__('代理不存在'));
        }
        if($agent_info['admin_id'] != $this->auth->id && $this->auth->id != 1)
        {
            $this->error('无权操作');
        }
        $tools_num_model = new Toolsnum();
        $tools_num_list = $tools_num_model->where('admin_id',$ids)->select();

        $tools_model = new Tools();
        $tools_list = $tools_model->where('admin_id',$agent_info['admin_id'])->select();
        if (!$tools_list) {
            $this->error('暂无工具');
        }

        if($this->request->isPost() === false)
        {

            $this->view->assign("tools_list", $tools_list);
            $this->view->assign("agent_num", $tools_num_list);
            return $this->view->fetch();
        }


        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $result = false;
        Db::startTrans();
        try {
            //循环参数，判断是否为代理、 代理次数是否足够
            foreach ($params as $k=>$v)
            {
                //只有新增才去判断代理次数是否足够
                if($params['type'] == 1)
                {
                    //
                    if($k != 'type' && !empty($v))
                    {
                        $tools_num_id_arr = explode('_',$k);

                        $result = $tools_num_model->where('id',$tools_num_id_arr[0])->where('admin_id',$row['id'])->setInc('num',$v);
                    }

                }else
                {
                    if($k != 'type' && !empty($v))
                    {
                        $tools_num_id_arr = explode('_',$k);
                        $result = $tools_num_model->where('id',$tools_num_id_arr[0])->where('admin_id',$row['id'])->setDec('num',$v);
                    }
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
