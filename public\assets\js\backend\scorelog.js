define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'scorelog/index' + location.search,
                    add_url: 'scorelog/add',
                    edit_url: 'scorelog/edit',
                    del_url: 'scorelog/del',
                    multi_url: 'scorelog/multi',
                    import_url: 'scorelog/import',
                    table: 'scorelog',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'users_id', title: __('Users_id')},
                        {field: 'oem_id', title: __('Oem_id')},
                        {field: 'agent_id', title: __('Agent_id')},
                        {field: 'inc_data', title: __('Inc_data'), searchList: {"1":__('Inc_data 1'),"2":__('Inc_data 2')}, formatter: Table.api.formatter.normal},
                        {field: 'dec_data', title: __('Dec_data'), searchList: {"1":__('Dec_data 1'),"2":__('Dec_data 2')}, formatter: Table.api.formatter.normal},
                        {field: 'price', title: __('Price')},
                        {field: 'before', title: __('Before')},
                        {field: 'after', title: __('After')},
                        {field: 'tools_id', title: __('Tools_id')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
                        {field: 'tools.name', title: __('Tools.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'users.nickname', title: __('Users.nickname'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
