<?php

return [
    'Id'             => 'ID',
    'Users_id'       => '用户',
    'Tools_id'       => '工具',
    'Paydata'        => '支付方式',
    'Paydata 1'      => '算力',
    'Paydata 2'      => '广告',
    'Paydata 3'      => '次数',
    'Name'          => '名称',
    'Paydata 4'      => '免费',
    'Price'          => '算力支出',
    'Content'        => '提交数据',
    'Status'         => '状态',
    'Status 1'       => '已提交',
    'Set status to 1'=> '设为已提交',
    'Status 2'       => '已完成',
    'Set status to 2'=> '设为已完成',
    'Status 3'       => '生成错误',
    'Set status to 3'=> '设为生成错误',
    'Error'          => '错误信息',
    'Success'        => '生成数据',
    'Createtime'     => '提交时间',
    'Endtime'        => '完成时间',
    'Oem_id'         => 'OEM归属',
    'Agent_id'       => '代理归属',
    'Users.nickname' => '昵称',
    'Admin.nickname' => '昵称',
    'Tools.name'     => '名称'
];
