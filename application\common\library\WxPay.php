<?php

namespace app\common\library;

use app\common\model\Admin;
use app\common\model\Oemconfig;
use app\common\model\Users;
use app\service\Utils;
use EasyWeChat\Factory;
use Intervention\Image\ImageManager;

class WxPay
{
    protected $config;
    protected $payment;
    protected $oem_id;

    public function __construct($oem_id)
    {
        $this->oem_id = $oem_id;
        $oem_config_model = new Oemconfig();
        $payConfig = $oem_config_model->where('group','in','wxpay,xcx,system')->where('admin_id',$oem_id)->select();
        if(!empty($payConfig)){
            foreach ($payConfig as $pay){
                $this->config[$pay['name']] = $pay['value'];
            }
        }
        $config = [
            'app_id' => $this->config['xcx_appid'],
            'mch_id' => $this->config['wx_mchid'],
            'key' => $this->config['wx_key'],
            'cert_path' => '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_cert.pem',
            'key_path' => '/www/wwwroot/szr-xcx.sanliankj.com.cn/cert/'.$oem_id.'/apiclient_key.pem',
            'notify_url' => $this->config['sys_url'].'/api/wxpay/wx_notify',
        ];
        $this->payment = Factory::payment($config);
    }
    
    /**
     * @param $order_sn 订单号
     * @param $openid 用户openid
     * @param $amount 金额
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function toBalance($order_sn,$openid,$amount)
    {
        $result = $this->payment->transfer->toBalance([
            'partner_trade_no' => $order_sn, // 商户订单号，需保持唯一性(只能是字母或者数字，不能包含有符号)
            'openid' => $openid,
            'check_name' => 'FORCE_CHECK', // NO_CHECK：不校验真实姓名, FORCE_CHECK：强校验真实姓名
            're_user_name' => '王小帅', // 如果 check_name 设置为FORCE_CHECK，则必填用户真实姓名
            'amount' => $amount * 100, // 企业付款金额，单位为分
            'desc' => '提现到账', // 企业付款操作说明信息。必填
        ]);
        return $result;
    }

    /**
     * 微信退款
     * @param $order_sn 微信订单号
     * @param $total_fee 订单金额
     * @param $refund_fee 退款金额
     * @return \think\response\Json
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public function refund($order_sn,$total_fee,$refund_fee)
    {
        $refundNumber = 'TK'.$this->oem_id.time();
        $result = $this->payment->refund->byTransactionId($order_sn,$refundNumber,$total_fee*100,$refund_fee*100);
        file_put_contents('wx_tk.txt',json_encode($result)."\r\n",FILE_APPEND);
        if($result['return_code'] == 'SUCCESS'){
            if($result['result_code'] == 'SUCCESS'){
                return ['code' => 1,'order_sn' => $order_sn];
            }else
            {
                return ['code' => 0,'msg' => $result['err_code_des']];
            }
        }else
        {
            return ['code' => 0,'msg' => $result['return_msg']];
        }
    }



    /**
     * 获取支付信息
     * @param $openid
     * @param $body
     * @param $out_trade_no
     * @param $total_fee
     * @param $trade_type
     * @return array|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createPayInfo($openid,$body,$out_trade_no,$total_fee,$trade_type='JSAPI')
    {
        $res = $this->payment->order->unify([
            'out_trade_no' => $out_trade_no,
            'total_fee' => $total_fee,
            'trade_type' => $trade_type,
            'openid' => $openid,
            'body' => $body,
        ]);
        // dump($res);
        if($res['return_code'] == 'SUCCESS' && $res['result_code'] == 'SUCCESS'){
            $config = $this->payment->jssdk->bridgeConfig($res['prepay_id'],false);
//            dump($config);
            return $config;
        }else
        {
            return ['code' => 0,'msg' => $res['return_msg']];
        }
//        return $res;
    }

    /**
     * 创建小程序二维码
     * @param $agent_id
     * @return \think\response\Json
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\RuntimeException
     */
    public function getXcxQrcode($agent_id)
    {
//        dump($this->config);
        $config = [
            'app_id' => $this->config['xcx_appid'],
            'secret' => $this->config['xcx_appsecret'],

            // 下面为可选项
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',

//            'log' => [
//                'level' => 'debug',
//                'file' => __DIR__.'/wechat.log',
//            ],
        ];
        $xcx_app = Factory::miniProgram($config);
        $params = [
            'agent_id' => $agent_id
        ];
        $scene = http_build_query($params);
        $optional = [
            'page' => 'pages/index/index',
            'check_path' => false
        ];
        $response = $xcx_app->app_code->getUnlimit($scene,$optional);
        $data = '';
        if($response instanceof \EasyWeChat\Kernel\Http\StreamResponse){
            $filename = $response->saveAs('./uploads/xcx/'.$this->oem_id.'/qrcode/agent',$agent_id.'.png');
            $qrCodePath = '/uploads/xcx/'.$this->oem_id.'/qrcode/agent/'.$agent_id.'.png';
            $utils = new Utils();

            $oss_result = $utils->uploadOss($qrCodePath,$this->oem_id);
            // dump($oss_result);

            if($oss_result['code'] == 1)
            {
                $upd_data = [
                    'agent_qrcode' => $oss_result['data']['url']
                ];
                $admin_model = new Admin();
                $res = $admin_model->save($upd_data,['id' => $agent_id]);
                $data = $oss_result['data']['url'];
            }else
            {
                $upd_data = [
                    'agent_qrcode' => $qrCodePath
                ];
                $admin_model = new Admin();
                $res = $admin_model->save($upd_data,['id' => $agent_id]);
                $data = $oss_result['data']['url'];
//                    $this->error($oss_result['msg']);
            }
            return json(['code' => 1,'msg' => '创建成功','data' => $data]);
        }else
        {
//            dump($response);
            return json(['code' => 0,'msg' => '创建失败']);
        }
    }
}