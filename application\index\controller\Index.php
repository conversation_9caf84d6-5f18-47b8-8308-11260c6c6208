<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Toolsorder;
use app\service\Utils;
use fast\Date;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        return $this->view->fetch();
    }

    public function test1()
    {

        $start_time = Date::unixtime('month');
        dump($start_time);
        die();
        $localTempFile = tempnam(sys_get_temp_dir(), 'oss_upload_');
        echo $localTempFile;
    }

    public function test()
    {
        $utils = new Utils();
//        $a = $utils->nlp('测试一下数据看看吧，还不清楚啥情况了，数据到底次没错？语句是否正常啊？',1);
        $a = $utils->chatAi('',1);
        dump($a);
    }

    public function callAiBack()
    {
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_xiezhen'
        ];
        $list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        $utils = new Utils();
        foreach($list as $k=>$v){
            $success = json_decode(json_decode($v['success'],true),true);
//            dump($success['JobId']);
            $params = [
                'JobId' => $success['JobId'],
            ];
            $result = $utils->getStatusPortraitJob($params,$v['oem_id']);
            if($result['code'] == 1){
                $data = json_decode($result['data'],true);
//                dump($data);
                if($data['JobStatusCode'] == 'DONE'){
                    //完成了
                    $imageUrl = $data['ResultUrls'][0];
                    //把图片上传到oss
                    // 本地临时文件名，用于保存从网址下载的文件
                    $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$v['users_id'].'.png';
                    // 下载网址文件到本地临时文件
                    file_put_contents('.'.$localTempFile, file_get_contents($imageUrl));
                    $oss_result = $utils->uploadOss($localTempFile,$v['oem_id']);
                    if($oss_result['code'] == 1) {

                        $success['image'] = $oss_result['data']['url'];
                        $upd_data['success'] = json_encode($success);
                        $upd_data['status'] = 2;
                        $upd_data['endtime'] = time();
                    }else
                    {
                        //上传oss失败
                        //失败了
                        $success['text'] = '上传oss失败'.$oss_result['msg'];
                        $upd_data['error'] = json_encode($success);;
                        $upd_data['status'] = 3;
                        $upd_data['endtime'] = time();
                    }
                }elseif ($data['JobStatusCode'] == 'FAIL'){
                    //失败了
                    $success['text'] = $result['data'];
                    $upd_data['error'] = json_encode($success);;
                    $upd_data['status'] = 3;
                    $upd_data['endtime'] = time();
                }else
                {
                    continue;
                }
            }else
            {
                $success['text'] = $result['msg'];
                $upd_data['error'] = json_encode($success);;
                $upd_data['status'] = 3;
                $upd_data['endtime'] = time();
            }
            $tools_order_model->save($upd_data,['id'=>$v['id']]);
        };
    }

    public function callAiVideoBack()
    {
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_tiaowu'
        ];
        $list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        $utils = new Utils();
        foreach($list as $k=>$v){
            $success = json_decode($v['success'],true);
//            dump($success['JobId']);
            $params = [
                'JobId' => $success['JobId'],
            ];
            $result = $utils->getVideoStatus($params,$v['oem_id']);
//            dump($result);
            if($result['code'] == 1){
                $data = json_decode($result['data'],true);
//                dump($data);
                if($data['Status'] == 'DONE'){
                    //完成了
                    $videoUrl = $data['ResultVideoUrl'];
                    //把图片上传到oss
                    // 本地临时文件名，用于保存从网址下载的文件
                    $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$v['users_id'].'.mp4';
                    // 下载网址文件到本地临时文件
                    file_put_contents('.'.$localTempFile, file_get_contents($videoUrl));
                    $oss_result = $utils->uploadOss($localTempFile,$v['oem_id']);
                    if($oss_result['code'] == 1) {

                        $success['video'] = $oss_result['data']['url'];
                        $upd_data['success'] = json_encode($success);
                        $upd_data['status'] = 2;
                        $upd_data['endtime'] = time();
                    }else
                    {
                        //上传oss失败
                        //失败了
                        $success['text'] = '上传oss失败'.$oss_result['msg'];
                        $upd_data['error'] = json_encode($success);;
                        $upd_data['status'] = 3;
                        $upd_data['endtime'] = time();
                    }
                }elseif ($data['Status'] == 'FAIL'){
                    //失败了
                    $success['text'] = $result['data'];
                    $upd_data['error'] = json_encode($success);;
                    $upd_data['status'] = 3;
                    $upd_data['endtime'] = time();
                }else
                {
                    continue;
                }
            }else
            {
                $success['text'] = $result['msg'];
                $upd_data['error'] = json_encode($success);;
                $upd_data['status'] = 3;
                $upd_data['endtime'] = time();
            }
            $tools_order_model->save($upd_data,['id'=>$v['id']]);
        }
    }

    public function getAudioStatus()
    {
        $tools_order_model = new Toolsorder();
        $where = [
            'status' => 1,
            'tools_rule' =>'ai_yinse',
            'success' => array('neq',''),
        ];
        $tools_order_list = $tools_order_model->where($where)->order('id asc')->limit(5)->select();
        foreach ($tools_order_list as $k=>$v){
            $success = json_decode($v['success'],true);
            if($success['TaskId'])
            {
                $params = [
                    'TaskId' => $success['TaskId'],
                ];
                $utils = new Utils();
                $result = $utils->DescribeVRSTaskStatus($params,$v['oem_id']);
//                dump($result);
                if($result['code'] == 1){
                    $arr = json_decode($result['data'],true);
                    if($arr['Data']['TaskId'] && $arr['Data']['Status'] == 2){
                        $success = [
                            'text' => $arr['Data']['FastVoiceType'],
                        ];
                        $upd_data = [
                            'error' => json_encode($success),
                            'status' => 2,
                            'endtime' => time(),
                        ];
                        $tools_order_model->save($upd_data,['id'=>$v['id']]);
                    }elseif (in_array($arr['Data']['Status'],[0,1]))
                    {
                        continue;
                    }else
                    {
                        $error = [
                            'text' => '声音复刻失败'.$result['data']
                        ];
                        $upd_data = [
                            'error' => json_encode($error),
                            'status' => 3,
                            'endtime' => time(),
                        ];
                        $tools_order_model->save($upd_data,['id'=>$v['id']]);
                    }
                }else
                {
                    $error = [
                        'text' => $result['msg'],
                    ];
                    $upd_data = [
                        'error' => json_encode($error),
                        'status' => 3,
                        'endtime' => time(),
                    ];
                    $tools_order_model->save($upd_data,['id'=>$v['id']]);
                }
            }else
            {
                continue;
            }

        }
    }

}
