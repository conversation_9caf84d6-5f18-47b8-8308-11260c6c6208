<?php

namespace app\common\model;

use think\Model;


class Scorelog extends Model
{

    

    

    // 表名
    protected $name = 'scorelog';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'inc_data_text',
        'dec_data_text'
    ];
    

    
    public function getIncDataList()
    {
        return ['1' => __('Inc_data 1'), '2' => __('Inc_data 2')];
    }

    public function getDecDataList()
    {
        return ['1' => __('Dec_data 1'), '2' => __('Dec_data 2')];
    }


    public function getIncDataTextAttr($value, $data)
    {
        $value = $value ?: ($data['inc_data'] ?? '');
        $list = $this->getIncDataList();
        return $list[$value] ?? '';
    }


    public function getDecDataTextAttr($value, $data)
    {
        $value = $value ?: ($data['dec_data'] ?? '');
        $list = $this->getDecDataList();
        return $list[$value] ?? '';
    }




    public function admin()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function tools()
    {
        return $this->belongsTo('Tools', 'tools_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function users()
    {
        return $this->belongsTo('Users', 'users_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
