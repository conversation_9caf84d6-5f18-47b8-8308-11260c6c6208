<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Admin;
use app\common\model\Oemconfig;
use app\common\model\Users;
use app\service\Inspect;
use app\service\Utils;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;
use fast\Random;
use Intervention\Image\ImageManager;
use think\Config;
use think\Validate;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'wxLogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';
    protected $xcx_app = null;
    protected $model = null;
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new Users();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
        $config = [
            'app_id' => $this->oemConfig['xcx']['xcx_appid'],
            'secret' => $this->oemConfig['xcx']['xcx_appsecret'],

            // 下面为可选项
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',

//            'log' => [
//                'level' => 'debug',
//                'file' => __DIR__.'/wechat.log',
//            ],
        ];
        $this->xcx_app = Factory::miniProgram($config);

    }


    /**
     * 修改用户信息
     * @return void
     */
    public function updateUsers()
    {
        $params = $this->request->param();
        $user_info = $this->auth->getUserInfo();
        $update = [];
        if(isset($params['nickname'])){
            $update['nickname'] = $params['nickname'];
        }
        if(isset($params['image'])){
            $update['image'] = $params['image'];
        }
        if(empty($update)){
            $this->error('无任何修改');
        }
        $res= $this->model->where('id',$user_info['id'])->update($update);
        if($res){
            $this->success('修改成功');
        }else
        {
            $this->error('修改失败');
        }
    }
    
    public function savePhone()
    {
        $user_info = $this->auth->getUserInfo();
        $params = $this->request->param();
        $session_info = $this->xcx_app->auth->session($params['code']);
        $data = $this->xcx_app->encryptor->decryptData($session_info['session_key'], $params['iv'], $params['encryptedData']);
        $users_model = new Users();
        $res = $users_model->where('id',$user_info['id'])->update(['phone' => $data['phoneNumber']]);
        if($res)
        {
            $this->success('授权成功');
        }else
        {
            $this->error('授权失败');
        }
    }


    public function wxLogin()
    {
        $code = $this->request->param('code');
        $scene = $this->request->param('scene');
//        $parent_id = $this->request->param('parent_id');
        if(empty($code)){
            $this->error('登录失败，信息错误');
        }
        $session_info = $this->xcx_app->auth->session($code);
        if(!$session_info || !isset($session_info['openid'])){
            $this->error('登录失败');
        }
        $users_info = $this->model->where('openid', $session_info['openid'])->find();
        if($users_info){
            $ret = $this->auth->direct($users_info['id']);
            $msg = '登录成功';
        }else
        {
            $admin_model = new Admin();
            $oem_url = $this->request->host();
            $oem_info = $admin_model->where('url',$oem_url)->where('id',$this->oemId)->find();
            if(!$oem_info){
                $this->error('小程序暂时无法使用');
            }
            $users_model = new Users();
            if($scene){
                $scene_arr = explode('=', $scene);

                if($scene_arr[0] == 'parent_id'){
                    $parent_id = $scene_arr[1];
                    $parent_info = $users_model->where('id', $parent_id)->find();
                    if($parent_info){
                        $parent_id = $parent_info['id'];
                        $j_parent_id = $parent_info['parent_id'];
                        $agent_id = $parent_info['agent_id'];
                    }else
                    {
                        $users_first = $users_model->where('oem_id',$this->oemId)->order('id asc')->find();
                        if($users_first){
                            $parent_id = $users_first['id'];
                            $j_parent_id = $users_first['parent_id'];
                            $agent_id = $users_first['agent_id'];
                        }else
                        {
                            $parent_id = 0;
                            $j_parent_id = 0;
                            $agent_id = 0;
                        }
                    }
                }else
                {
                    $parent_id = 0;
                    $j_parent_id = 0;
                    $agent_id = $scene_arr[1];
                }

            }else
            {
                $users_first = $users_model->where('oem_id',$this->oemId)->where('parent_id',0)->order('id asc')->find();
                if($users_first){
                    $parent_id = $users_first['id'];
                    $j_parent_id = $users_first['parent_id'];
                    $agent_id = $users_first['agent_id'];
                }else
                {
                    $parent_id = 0;
                    $j_parent_id = 0;
                    $agent_id = 0;
                }
            }
//            dump($parent_id);
//            dump($j_parent_id);
//            dump($agent_id);
//            die();
            $ret = $this->auth->wxRegister($session_info['openid'],$parent_id,$j_parent_id,$oem_info['id'],$agent_id);
            $msg = '注册成功';
        }
        if ($ret) {
            $user_info = $this->auth->getUserinfo();
            if($msg == '注册成功')
            {
                $inspect = new Inspect();
                $inspect->addUsersToolsNum($user_info['id'],$user_info['oem_id']);
            }

            $data = ['userinfo' => $user_info];
            $this->success($msg, $data);
        } else {
            $this->error($this->auth->getError());
        }

    }

    /**
     * 创建海报
     * @return void
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\RuntimeException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getUserQrcode()
    {
        $user_info = $this->auth->getUserInfo();
        if($user_info['qrcode'])
        {
            $this->success('ok', $user_info['qrcode']);
        }
        $params = [
            'parent_id' => $user_info['id']
        ];
        $scene = http_build_query($params);
        $optional = [
            'page' => 'pages/index/my',
            'env_version' => 'release',
            'check_path' => false
        ];
        $response = $this->xcx_app->app_code->getUnlimit($scene,$optional);
        $data = '';
        if($response instanceof \EasyWeChat\Kernel\Http\StreamResponse){
            $filename = $response->saveAs('./uploads/xcx/'.$this->oemId.'/qrcode',$user_info['id'].'.png');
            $qrCodePath = './uploads/xcx/'.$this->oemId.'/qrcode/'.$user_info['id'].'.png';
            $utils = new Utils();
            $oss_config = $utils->getOemConfig($this->oemId,'rebate');
//        dump($oss_config);
            if(!$oss_config || $oss_config['poster'] == '')
            {
                $this->error('没有推广海报');
            }
            // 打开本地二维码图片
            // 创建 ImageManager 实例
            $manager = new ImageManager();
            $qrCodeImage = $manager->make($qrCodePath);
            // 将二维码裁剪成圆形
            $qrCodeWidth = $qrCodeImage->width();
            $qrCodeHeight = $qrCodeImage->height();
            $radius = min($qrCodeWidth, $qrCodeHeight) / 2;
            $circle = $manager->canvas($qrCodeWidth, $qrCodeHeight, '#000000');
            $circle->circle($radius * 2, $radius, $radius, function ($draw) {
                $draw->background('#ffffff');
            });
            $qrCodeImage->mask($circle, false);

            // 从网络获取海报图片
            $posterImage = $manager->make($oss_config['poster']);
            // 获取海报和二维码的尺寸
            $posterWidth = $posterImage->width();
            $posterHeight = $posterImage->height();
            $qrCodeWidth = $qrCodeImage->width();
            $qrCodeHeight = $qrCodeImage->height();
            // 计算二维码的位置（右下角）
            $x = $posterWidth - $qrCodeWidth - 325; // 距离右边框20像素
            $y = $posterHeight - $qrCodeHeight - 51; // 距离下边框20像素

            // 将二维码添加到海报上
            $posterImage->insert($qrCodeImage, 'top-left', $x, $y);
            // 保存图片到临时文件
            $tempFilePath = '/uploads/xcx/'.$this->oemId.'/qrcode/'.'haibao_'.time().'_'.$user_info['id'].'.png';
            $posterImage->save('.'.$tempFilePath);
            $oss_result = $utils->uploadOss($tempFilePath,$this->oemId);
            if($oss_result['code'] == 1)
            {
                $upd_data = [
                    'qrcode' => $oss_result['data']['url'],
                    'xcx_qrcode' => $qrCodePath
                ];
                $users_model = new Users();
                $res = $users_model->save($upd_data,['id' => $user_info['id']]);
                $data = $oss_result['data']['url'];
            }else
            {
                $upd_data = [
                    'qrcode' => 'https://'.$this->request->host().$tempFilePath,
                    'xcx_qrcode' => $qrCodePath
                ];
                $users_model = new Users();
                $res = $users_model->save($upd_data,['id' => $user_info['id']]);
                $data = 'https://'.$this->request->host().$tempFilePath;
//                    $this->error($oss_result['msg']);
            }
            $this->success('ok',$data);
        }else
        {
//            dump($response);
            $this->error('创建海报失败');
        }
    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }
    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('请求成功'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $bio = $this->request->post('bio');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
}
