<form id="tools-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[type]-1"><input id="row[type]-1" name="row[type]" type="radio" value="1"  checked /> 增加</label>
                <label for="row[type]-2"><input id="row[type]-2" name="row[type]" type="radio" value="2"  /> 减少</label>
            </div>

        </div>
    </div>
    {foreach name="tools_list" item="row"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{$row.name}：</label>
        <div class="col-xs-12 col-sm-8">
            {foreach name="agent_num" item="u_num"}
            {if condition="$u_num['tools_id'] eq $row['id']"}
            <div class="col-sm-6">
                <input class="form-control " name="row[{$u_num.id}_{$u_num.tools_id}]" type="number">
            </div>
            <div class="col-sm-8" style="color: #1688f1">
                用户剩余次数：{$u_num.num}
            </div>


            {/if}
            {/foreach}
        </div>
    </div>
    {/foreach}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
