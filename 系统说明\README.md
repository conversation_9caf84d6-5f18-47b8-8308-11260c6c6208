# 小程序后台管理系统 - 系统说明文档

## 文档概述

本文档集为基于FastAdmin框架开发的微信小程序后台管理系统提供全面的技术文档和开发指南。系统主要提供AI工具服务，包括AI绘画、语音识别、OCR文字识别、AI写真等功能，支持多租户架构。

## 文档目录

### 📋 [项目概述](./项目概述.md)
- **内容**：项目基本信息、技术栈、系统架构、核心功能
- **适用人群**：项目经理、技术负责人、新接手的开发者
- **重点内容**：
  - 技术栈详细说明（FastAdmin + ThinkPHP 5.1）
  - 第三方服务集成概览
  - 多租户架构设计
  - 系统安全特性

### 🔌 [API接口文档](./API接口文档.md)
- **内容**：所有API接口的详细说明、参数、响应格式
- **适用人群**：前端开发者、接口调用方、测试人员
- **重点内容**：
  - 用户相关接口（微信登录、用户信息管理）
  - AI工具接口（绘画、识别、语音合成等）
  - 支付相关接口（订单创建、回调处理）
  - 文件上传接口（OSS配置获取）
  - 统一响应格式和错误码

### 🗄️ [数据库设计文档](./数据库设计文档.md)
- **内容**：数据库表结构、字段说明、关系设计
- **适用人群**：后端开发者、数据库管理员、系统架构师
- **重点内容**：
  - 核心业务表设计（用户、工具、订单、VIP等）
  - 多租户数据隔离机制
  - 表关系和索引设计
  - 数据库优化建议

### ☁️ [第三方服务集成文档](./第三方服务集成文档.md)
- **内容**：所有第三方服务的集成方式、配置说明
- **适用人群**：后端开发者、运维人员、系统集成人员
- **重点内容**：
  - 微信生态服务（小程序、支付）
  - 腾讯云AI服务（绘画、识别、语音等）
  - 阿里云存储服务（OSS）
  - 配置管理和安全考虑

### ⚙️ [系统配置文档](./系统配置文档.md)
- **内容**：系统配置项、环境变量、部署配置
- **适用人群**：运维人员、系统管理员、部署人员
- **重点内容**：
  - 核心配置文件说明
  - 环境变量配置
  - Web服务器配置（Nginx/Apache）
  - 安全配置和监控设置

### 📊 [业务流程文档](./业务流程文档.md)
- **内容**：主要业务流程的运行逻辑和原理
- **适用人群**：产品经理、业务分析师、开发者
- **重点内容**：
  - 用户注册登录流程
  - AI工具使用流程
  - VIP会员购买流程
  - 支付结算流程
  - 多租户管理流程

### 🛠️ [开发指南](./开发指南.md)
- **内容**：开发环境搭建、开发规范、常见任务指南
- **适用人群**：新接手的开发者、实习生、外包团队
- **重点内容**：
  - 快速上手指南
  - 代码规范和最佳实践
  - 常见开发任务示例
  - 调试技巧和问题排查

## 系统特色

### 🏢 多租户架构
- 支持多个小程序共享同一套后台系统
- 每个租户拥有独立的配置和数据隔离
- 三级权限管理：超管 -> OEM -> 代理商

### 🤖 AI服务集成
- **AI绘画**：文生图、图生图、风格转换
- **AI写真**：人像生成和风格转换
- **语音识别**：音频转文字
- **OCR识别**：图片文字识别
- **语音合成**：文字转语音

### 💰 完整支付体系
- 微信小程序支付
- VIP会员订阅
- 工具按次付费
- 推广分佣系统

### ☁️ 云服务集成
- 腾讯云AI服务全套集成
- 阿里云OSS存储
- 腾讯云COS存储
- 百度千帆大模型

## 技术栈

### 后端技术
- **框架**：FastAdmin (基于ThinkPHP 5.1)
- **数据库**：MySQL 8.0
- **PHP版本**：7.4+
- **缓存**：Redis

### 前端技术
- **后台管理**：AdminLTE + Bootstrap
- **小程序**：微信小程序原生开发
- **构建工具**：Grunt + RequireJS

### 第三方服务
- **微信**：小程序SDK、支付SDK
- **腾讯云**：AI绘画、语音识别、OCR、混元大模型
- **阿里云**：对象存储OSS
- **百度云**：千帆大模型

## 快速开始

### 1. 环境要求
- PHP 7.4+ (推荐 PHP 8.0+)
- MySQL 5.7+ 或 MySQL 8.0+
- Nginx 1.18+ 或 Apache 2.4+
- Composer 2.0+

### 2. 安装步骤
```bash
# 1. 克隆项目
git clone [项目地址]
cd szr-xcx.sanliankj.com.cn

# 2. 安装依赖
composer install

# 3. 配置环境
cp .env.sample .env
vim .env

# 4. 设置权限
chmod -R 777 runtime/
chmod -R 777 public/uploads/

# 5. 配置Web服务器
# 参考系统配置文档
```

### 3. 访问系统
- **前台地址**：http://your-domain.com
- **后台地址**：http://your-domain.com/admin
- **API地址**：http://your-domain.com/api

## 文档使用建议

### 🎯 按角色阅读
- **项目经理**：项目概述 → 业务流程文档
- **前端开发**：API接口文档 → 开发指南
- **后端开发**：数据库设计 → 第三方服务集成 → 开发指南
- **运维人员**：系统配置文档 → 第三方服务集成
- **新人入职**：项目概述 → 开发指南 → 其他相关文档

### 📚 按开发阶段阅读
- **项目了解阶段**：项目概述 → 业务流程文档
- **环境搭建阶段**：开发指南 → 系统配置文档
- **功能开发阶段**：API接口文档 → 数据库设计文档
- **集成测试阶段**：第三方服务集成文档
- **部署上线阶段**：系统配置文档

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2025-01-28 | 初始版本，完整的系统文档 | AI助手 |

## 联系方式

如有问题或建议，请联系：
- **技术支持**：[技术负责人联系方式]
- **项目管理**：[项目经理联系方式]
- **文档维护**：[文档维护人联系方式]

## 注意事项

1. **文档同步**：代码更新时请及时更新相关文档
2. **配置安全**：生产环境请修改默认密钥和配置
3. **备份重要**：定期备份数据库和重要文件
4. **监控告警**：建议配置系统监控和告警机制
5. **日志管理**：定期清理和归档系统日志

---

**文档版本**：v1.0  
**最后更新**：2025年1月28日  
**文档状态**：✅ 完整
