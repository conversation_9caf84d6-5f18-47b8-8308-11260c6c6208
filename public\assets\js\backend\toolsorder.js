define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            if(Config.show)
            {
                var delurl = 'toolsorder/del';
                var editurl = 'toolsorder/edit';
            }else
            {
                var delurl = '';
                var editurl = '';
            }
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'toolsorder/index' + location.search,
                    add_url: 'toolsorder/add',
                    edit_url: editurl,
                    del_url: delurl,
                    multi_url: 'toolsorder/multi',
                    import_url: 'toolsorder/import',
                    table: 'toolsorder',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'users.nickname', title: __('Users_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'tools.name', title: __('Tools_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'paydata', title: __('Paydata'), searchList: {"1":__('Paydata 1'),"2":__('Paydata 2'),"3":__('Paydata 3'),"4":__('Paydata 4')}, formatter: Table.api.formatter.normal},
                        {field: 'price', title: __('Price'),operate: false},
                        {field: 'oem.nickname', title: __('Oem_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'agent.nickname', title: __('Agent_id'),operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:false, addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'endtime', title: __('Endtime'), operate:false, addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    title: __('提交数据'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    text: __('提交数据'),
                                    url: 'toolsorder/content?type=1',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                },
                                {
                                    name: 'detail',
                                    title: __('生成数据'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    text: __('生成数据'),
                                    url: 'toolsorder/content?type=2',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 2)
                                        {
                                            return true;
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },
                                {
                                    name: 'detail',
                                    title: __('错误信息'),
                                    classname: 'btn btn-xs btn-danger btn-dialog',
                                    text: __('错误信息'),
                                    url: 'toolsorder/content?type=3',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 3)
                                        {
                                            return true;
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },


                            ], formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
