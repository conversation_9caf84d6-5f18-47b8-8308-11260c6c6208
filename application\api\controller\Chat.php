<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Sllog;
use app\common\model\Toolsorder;
use app\common\model\Users;
use app\service\Inspect;
use app\service\Utils;
use think\Db;

class Chat extends Api
{


    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     *
     * AI生成文案
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getTextCreateAi()
    {
        $user_info = $this->auth->getUserinfo();
        $tools_order_model = new Toolsorder();
        $tools_model = new \app\common\model\Tools();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_wenan')->find();
        if(!$tools_info){
            $this->error('工具暂无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        $text = $this->request->post('question');
        if(!$text){
            $this->error('文案不能为空');
        }
        $add_msg = [[
            'id' => 1,
            'text' => $text,
            'model' => 1,
            'create_time' => time(),
        ]];
        $add = [
            'name' => $tools_info['name'],
            'users_id' => $user_info['id'],
            'tools_id' => $tools_info['id'],
            'oem_id' => $user_info['oem_id'],
            'agent_id' => $user_info['agent_id'],
            'paydata' => 1,
            'price' => $tools_info['price'],
            'status' => 1,
            'tools_rule' => $tools_info['rule'],
            'content' => json_encode($add_msg),
        ];
        $ret = $tools_order_model->save($add);
        if(!$ret){
            $this->error('使用失败');
        }
        $tools_order_id = $tools_order_model->id;
        $content = '';
        try {
            $tools_order_info = $tools_order_model->where('id',$tools_order_id)->find();
            $send_msg = [
                [
                    "role" => "user",
                    "content" => $text,
                ]
            ];

            $utils = new Utils();
            $result = $utils->chatAi($send_msg,$this->oemId);
            if($result['code'] == 1)
            {
                $content = $result['data']['choices'][0]['message']['content'];
                $success_msg= [[
                    'id' =>  1,
                    'text' => $content,
                    'model' => 1,
                    'create_time' => time(),
                ]];
                $upd_data = [
                    'success' => json_encode($success_msg),
                    'status' => 2,
                ];
                $tools_order_info->save($upd_data);
//                $users_model = new Users();
//                $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
                $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            }else
            {
                exception($result['msg']['error']['message']);
            }
        }catch (\Exception $e){
            $this->error($e->getMessage());
        }
        $this->success('ok',$content);





    }

    /**
     * 获取某条记录的最新10条聊天内容
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAiMessageList()
    {
        $user_info = $this->auth->getUserInfo();
        $messageId = $this->request->post('messageId');
        $tools_oder_model = new Toolsorder();
        $tools_order_info = $tools_oder_model->where('id',$messageId)->where('oem_id',$this->oemId)->where('users_id',$user_info['id'])->find();
        if(!$tools_order_info)
        {
            $this->error('获取失败');
        }
        $send_message = json_decode($tools_order_info['content'],true);
        $ref_message = json_decode($tools_order_info['success'],true);
        // 获取两个数组的长度
        $len1 = count($send_message);
        $len2 = count($ref_message);

        // 遍历两个数组，交叉添加元素
        $newArray = [];
        for ($i = 0; $i < max($len1, $len2); $i++) {
            if ($i < $len1) {
                $newArray[] = [
                    'role' => 'user',
                    'question' => $send_message[$i]['text'],
                ];
            }
            if ($i < $len2) {
                $newArray[] = [
                    'role' => 'assistant',
                    'content' => $ref_message[$i]['text'],
                ];
            }
        }
        $this->success('ok',$newArray);
    }


    /**
     * 获取历史记录列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getHistoryMessage()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_order_model = new Toolsorder();
        $where = [
            'users_id' => $user_info['id'],
            'tools_rule' => 'ai_chat',
            'oem_id' => $this->oemId
        ];
        $message_list = $tools_order_model->where($where)->order('createtime desc')->limit(10)->select();
        $message_data = [];
        foreach ($message_list as $k=>$message) {
            $date = date('Y-m-d', $message['createtime']);
            if (!isset($message_data[$date])) {
                $message_data[$date] = [];
            }

            $message_data[$date][] = $message;
        }
        $new_message = [];
        $i = 0;
        foreach ($message_data as $k=>$message) {
            $new_message[$i]['date'] = $k;
            foreach ($message as $k1=>$v) {
                $content = json_decode($v['content'],true);
                $first_content = mb_substr($content[0]['text'],0,10);
//                $active = false;
//                if($i == 0 && $k1 == 0)
//                {
//                    $active = true;
//                }
                $new_message[$i]['deepseekMessageList'][$k1] = [
                    'id' => $v['id'],
                    'question' => $first_content,
                    'active' => false
                ];
            }
            $i++;
        }

//        dump($new_message);
//        die();
        $this->success('ok',$new_message);
    }
    /**
     * 创建会话
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCreateAIMessage()
    {

        $user_info = $this->auth->getUserInfo();
        $tools_model = new \app\common\model\Tools();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_chat')->find();
        if(!$tools_info){
            $this->error('工具不存在');
        }

//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }

        $tools_order_model = new Toolsorder();

        $params = $this->request->param();
        $messageId = $params['messageId'];


        Db::startTrans();
        try {
            if(empty($messageId)){
                $add_msg = [[
                    'id' => 1,
                    'text' => $params['content'],
                    'model' => 1,
                    'create_time' => time(),
                ]];
                $add = [
                    'name' => $tools_info['name'],
                    'users_id' => $user_info['id'],
                    'tools_id' => $tools_info['id'],
                    'paydata' => 1,
                    'content' => json_encode($add_msg),
                    'price' => $tools_info['price'],
                    'status' => 1,
                    'oem_id' => $user_info['oem_id'],
                    'agent_id' => $user_info['agent_id'],
                    'tools_rule' => 'ai_chat',
                ];
                $tools_order_model->save($add);
                $messageId = $tools_order_model->id;
                $id = 1;
            }else
            {
                $tools_order_info = $tools_order_model->where('id',$messageId)->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->find();
                if(!$tools_order_info){
                    exception('请重新建立对话');
//                    $this->error('请重新建立对话');
                }
                $content_msg = json_decode($tools_order_info['content'],true);
                $old_count = count($content_msg);
                $content_msg[] = [
                    'id' => count($content_msg) + 1,
                    'text' => $params['content'],
                    'model' => 1,
                    'create_time' => time(),
                ];
                $upd_data = [
                    'content' => json_encode($content_msg),
                ];
                $tools_order_info->save($upd_data);
                $messageId = $tools_order_info['id'];
                $id = $old_count + 1;
            }
//            $users_model = new Users();
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
//            Sllog::addLog($user_info,$tools_info,'AI对话');
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            Db::commit();
        } catch (\Exception $e)
        {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $redata = [
            'messageId' => $messageId,
            'id' => $id
        ];
        $this->success('ok',$redata);

//        ["oem_id"] => string(1) "3"
//        ["content"] => string(3) "123"
//        ["model"] => string(2) "ds"
//        dump($params);


    }


    /**
     * AI对话
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getOneAiMessage()
    {
        $params = $this->request->param();
        $tools_order_model = new Toolsorder();
        $user_info = $this->auth->getUserInfo();
        $tools_order_info = $tools_order_model->where('id',$params['messageId'])->where('users_id',$user_info['id'])->where('oem_id',$this->oemId)->find();
        if (!$tools_order_info) {
            $this->error('请重新建立对话1');
        }
        $content_msg = json_decode($tools_order_info['content'],true);
        $new_msg = $content_msg[count($content_msg)-1];
        if($params['id'] == $new_msg['id'])
        {
            $send_msg = json_decode($tools_order_info['content'],true);
            $ret_msg = json_decode($tools_order_info['success'],true);
            if(count($send_msg) > 1 && count($ret_msg) > 0)
            {
                $messages = [
                        [
                            "role" => "user",
                            "content" => $send_msg[count($send_msg) - 2]['text'],
                        ],
                        [
                            "role" => "assistant",
                            "content" => $ret_msg[count($ret_msg) - 1]['text'],
                        ],
                        [
                            "role" => "user",
                            "content" => $send_msg[count($send_msg) - 1]['text'],
                        ],
                ];
            }else
            {
                $messages = [
                    [
                        "role" => "user",
                        "content" => $new_msg['text'],
                    ]
                ];
            }
            $content = '';
            Db::startTrans();
            try {
                $utils = new Utils();
                $result = $utils->chatAi($messages,$this->oemId);
                if($result['code'] == 1)
                {
                    $content = $result['data']['choices'][0]['message']['content'];
                    if(empty($tools_order_info['success']))
                    {
                        $success_msg= [[
                            'id' =>  1,
                            'text' => $content,
                            'model' => 1,
                            'create_time' => time(),
                        ]];
                    }else
                    {
                        $success_msg = json_decode($tools_order_info['success'],true);
                        $success_msg[] = [
                            'id' => count($success_msg) + 1,
                            'text' => $content,
                            'model' => 1,
                            'create_time' => time(),
                        ];
                    }
                    $upd_data = [
                        'success' => json_encode($success_msg),
                        'status' => 2
                    ];
                    $tools_order_info->save($upd_data);
                }else
                {
                    exception($result['msg']['error']['message']);
                }
                Db::commit();
            }catch (\Exception $e)
            {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $data = [
                'content' => $content,
            ];
            $this->success('ok',$data);
        }else
        {
            $this->error('请重新建立对话2');
        }

    }
}