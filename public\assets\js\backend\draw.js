define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'draw/index' + location.search,
                    add_url: 'draw/add',
                    edit_url: '',
                    del_url: 'draw/del',
                    multi_url: 'draw/multi',
                    import_url: 'draw/import',
                    table: 'draw',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'number', title: __('Number'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'wx_number', title: __('Wx_number'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'users.nickname', title: __('Users_id')},
                        {field: 'money', title: __('Money'), operate:false},
                        {field: 'commission', title: __('Commission'), operate:false},
                        {field: 'commission_actual', title: __('Commission_actual'), operate:false},
                        {field: 'before', title: __('Before'), operate:false},
                        {field: 'after', title: __('After'), operate:false},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3'),"4":__('Status 4')}, formatter: Table.api.formatter.status},
                        {field: 'oem.nickname', title: __('Oem_id'),operate: Config.show ? 'LIKE' : Config.show,visible:Config.show},
                        {field: 'agent.nickname', title: __('Agent_id'),operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'endtime', title: __('Endtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                        buttons: [
                                {
                                    name: 'ajax',
                                    title: __('确认打款'),
                                    classname: 'btn btn-xs btn-danger  btn-magic btn-ajax',
                                    text:"打款",
                                    confirm: '确认打款，是否继续？',
                                    url: 'draw/withdrawal',
                                    success: function (data, ret) {
                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 1)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },
                                {
                                    name: 'ajax',
                                    title: __('驳回打款'),
                                    classname: 'btn btn-xs btn-info  btn-magic btn-ajax',
                                    text:"驳回",
                                    confirm: '驳回打款，是否继续？',
                                    url: 'draw/reject',
                                    success: function (data, ret) {
                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 1)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },


                            ],formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
