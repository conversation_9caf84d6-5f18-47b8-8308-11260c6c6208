# 项目概述

## 项目基本信息

### 项目名称
小程序后台管理系统 (szr-xcx.sanliankj.com.cn)

### 项目描述
基于FastAdmin框架开发的微信小程序后台管理系统，主要提供AI工具服务，包括AI绘画、语音识别、OCR文字识别、AI写真等功能。系统支持多租户架构，可为不同的小程序提供独立的配置和服务。

### 版本信息
- 系统版本：1.0.1
- FastAdmin版本：1.6.0.20250331
- ThinkPHP版本：5.1.x (dev-master)
- PHP版本要求：>=7.4.0

## 技术栈

### 后端技术
- **框架**：FastAdmin (基于ThinkPHP 5.1)
- **数据库**：MySQL 8.0
- **PHP版本**：7.4+
- **Web服务器**：Nginx/Apache

### 前端技术
- **后台管理**：AdminLTE + Bootstrap
- **JavaScript**：RequireJS模块化管理
- **样式**：Less预处理器

### 第三方服务集成
- **微信生态**：
  - 微信小程序SDK (overtrue/wechat ^4.6)
  - 微信支付 (wechatpay/wechatpay ^1.4)
  
- **腾讯云服务**：
  - AI绘画服务 (tencentcloud/aiart ^3.0)
  - 语音识别 (tencentcloud/asr ^3.0)
  - OCR文字识别 (tencentcloud/ocr ^3.0)
  - 混元大模型 (tencentcloud/hunyuan ^3.0)
  - 语音合成 (tencentcloud/vrs ^3.0)
  - 视频理解 (tencentcloud/vclm ^3.0)
  
- **阿里云服务**：
  - 对象存储OSS (aliyuncs/oss-sdk-php ^2.7)
  
- **腾讯云存储**：
  - 对象存储COS (qcloud/cos-sdk-v5 ^2.6)

### 开发工具
- **依赖管理**：Composer
- **构建工具**：Grunt
- **图像处理**：Intervention Image ^2.7
- **Excel处理**：PhpSpreadsheet ^1.29.1

## 系统架构

### 目录结构
```
szr-xcx.sanliankj.com.cn/
├── application/           # 应用目录
│   ├── admin/            # 后台管理模块
│   ├── api/              # API接口模块
│   ├── index/            # 前台模块
│   ├── common/           # 公共模块
│   └── service/          # 服务层
├── public/               # 公共资源目录
├── thinkphp/            # ThinkPHP框架核心
├── vendor/              # Composer依赖包
├── addons/              # 插件目录
├── cert/                # SSL证书目录
├── runtime/             # 运行时缓存目录
└── extend/              # 扩展类库目录
```

### 模块说明

#### 1. Admin模块 (后台管理)
- **用户管理**：小程序用户信息管理
- **工具管理**：AI工具配置和管理
- **订单管理**：工具使用订单和VIP订单管理
- **系统配置**：多租户配置管理
- **权限管理**：基于Auth的权限控制系统

#### 2. API模块 (接口服务)
- **用户接口**：微信登录、用户信息管理
- **工具接口**：AI工具调用接口
- **支付接口**：微信支付集成
- **上传接口**：文件上传到云存储

#### 3. Service层 (业务服务)
- **Utils服务**：第三方API调用封装
- **WxTransferService**：微信转账服务
- **Inspect服务**：用户权限和额度检查

## 核心功能

### 1. 多租户架构
- 支持多个小程序共享同一套后台系统
- 每个租户拥有独立的配置和数据隔离
- 支持代理商模式，三级权限管理

### 2. AI工具服务
- **AI绘画**：基于腾讯云AI绘画服务
- **AI写真**：人像生成和风格转换
- **语音识别**：音频转文字
- **OCR识别**：图片文字识别
- **语音合成**：文字转语音

### 3. 支付系统
- 微信小程序支付
- VIP会员订阅
- 工具按次付费
- 商家转账到零钱

### 4. 云存储集成
- 阿里云OSS存储
- 腾讯云COS存储
- 自动文件上传和管理

## 数据库设计

### 主要数据表
- `fa_admin`：管理员表（支持多租户）
- `fa_users`：小程序用户表
- `fa_tools`：AI工具配置表
- `fa_toolsorder`：工具使用订单表
- `fa_vip`：VIP套餐表
- `fa_viporder`：VIP订单表
- `fa_oemconfig`：多租户配置表

## 部署环境

### 服务器要求
- **操作系统**：Linux/Windows
- **Web服务器**：Nginx 1.18+ 或 Apache 2.4+
- **PHP版本**：7.4+ (推荐8.0+)
- **数据库**：MySQL 5.7+ 或 MySQL 8.0+
- **内存**：建议4GB+
- **存储**：建议SSD硬盘

### 域名配置
- 主域名：szr-xcx.sanliankj.com.cn
- 支持多域名绑定不同租户

## 安全特性

### 1. 权限控制
- 基于FastAdmin的Auth权限系统
- 支持角色和权限的细粒度控制
- API接口token验证

### 2. 数据安全
- 数据库连接加密
- 敏感信息配置文件保护
- 文件上传安全检查

### 3. 接口安全
- CORS跨域请求控制
- IP白名单限制
- 请求频率限制

## 开发规范

### 1. 代码规范
- 遵循PSR-4自动加载规范
- 使用ThinkPHP 5.1开发规范
- 统一的错误处理和日志记录

### 2. 数据库规范
- 统一表前缀：fa_
- 时间戳字段：createtime, updatetime
- 软删除支持

### 3. API规范
- RESTful API设计
- 统一的响应格式
- 完整的错误码定义

## 监控和日志

### 1. 系统日志
- 运行时日志：runtime/log/
- 错误日志自动记录
- 管理员操作日志

### 2. 业务监控
- 用户行为统计
- 工具使用统计
- 支付订单监控

## 扩展性

### 1. 插件系统
- 基于FastAdmin插件机制
- 支持在线安装和卸载
- 插件独立配置和数据

### 2. 第三方集成
- 标准化的第三方服务接口
- 配置化的服务切换
- 服务降级和容错机制

## 维护说明

### 1. 定期维护
- 数据库备份
- 日志文件清理
- 缓存清理

### 2. 更新升级
- 框架版本更新
- 第三方服务SDK更新
- 安全补丁更新

---

**注意**：本文档基于当前系统版本编写，如有更新请及时同步文档内容。
