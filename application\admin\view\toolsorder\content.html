<table class="table table-striped">
    
    <tbody>
        {foreach name="content" item="vo" key="key"}
                {if condition="$key eq 'text'"}
                <tr>
                    <td>文字</td>
                    <td style="word-break: break-all;">{$vo|htmlentities}</td>
                </tr>
                {elseif condition="$key eq 'image'"}
                <tr>
                    <td>图片</td>
                    <td style="word-break: break-all; ">
                        <img src="{$vo|htmlentities}" style="max-width: 200px; height: auto;" alt="">
                    </td>
                </tr>
                {elseif condition="$key eq 'video'"}
                <tr>
                    <td>视频</td>
                    <td style="word-break: break-all;"><video style="max-width: 200px; height: auto;"  src="{$vo|htmlentities}" controls></video></td>
                </tr>
                {elseif condition="$key eq 'audio'"}
                <tr>
                    <td>音频</td>
                    <td style="word-break: break-all;"> <audio src="{$vo|htmlentities}" controls></audio></td>
                </tr>
                {elseif condition="$key eq 'file'"}
                <tr>
                    <td>文件</td>
                    <td style="word-break: break-all;"><a href="{$vo|htmlentities}" target="_blank"></a></td>
                </tr>
                {elseif condition="$key eq 'link'"}
                <tr>
                    <td>链接</td>
                    <td style="word-break: break-all;"><a href="{$vo|htmlentities}" target="_blank"></a></td>
                </tr>
                {elseif condition="$key eq 'expand'"}
                <tr>
                    <td>拓展</td>
                    <td style="word-break: break-all;">{$vo|htmlentities}</td>
                </tr>
                {/if}
        {/foreach}
    </tbody>
</table>