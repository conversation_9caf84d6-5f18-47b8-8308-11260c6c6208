<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Toolsorder;
use app\common\model\Users;
use app\service\Inspect;
use app\service\Utils;
use think\Db;

class Tools extends Api
{

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $model = null;
    public function __initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Tools();
    }

    public function getChatMessageList()
    {
        $user_info = $this->auth->getUserInfo();
        $where = [
            'users_id' => $user_info['id'],
            'tools_rule' => 'ai_chat',
            'oem_id' => $this->oemId,
            'content' => array('neq',null)
        ];
        $message_list = $this->model->field('DATE(createtime) as date, content')->where($where)->order('id desc')->limit(10)->select();
//        $data = [];
//        foreach ($message_list as $k=>$message) {
//            $content = json_decode($message['content'],true);
//            $first_content = substr($content[0]['text'],0,10);
//            $data[$k]['text'] = $first_content;
//        }
        $this->success('ok',$message_list);
    }


    /**
     * AI绘画生成
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createImgByTx()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_huihua')->find();
        if(!$tools_info){
            $this->error('工具不存在');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        Db::startTrans();
        try {
            $tx_params = [
                'Prompt' => $params['prompt'],
                'NegativePrompt' => $params['negativePrompt'],
                'Style' => $params['style'] == 'auto' ? '000' : $params['style'],
                'Resolution' => $params['width'].':'.$params['height'],
                'RspImgType' => 'url'
            ];
            $content = [
                'text' =>'关键词:'.$params['prompt'].';不要出现关键词：'.$params['negativePrompt'].';风格：'.$params['style'],
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => 'ai_huihua',
            ];
            $tools_order_result = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $utils = new Utils();
            $result = $utils->TextToImageLiteByTx($tx_params,$this->oemId);
            if($result['code'] == 1)
            {
                //把图片上传到oss
                // 本地临时文件名，用于保存从网址下载的文件
                $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$user_info['id'].'.jpg';
                // 下载网址文件到本地临时文件
                file_put_contents('.'.$localTempFile, file_get_contents($result['data']));
                $oss_result = $utils->uploadOss($localTempFile,$this->oemId);
                if($oss_result['code'] == 1)
                {
                    $ref_msg = [
                        'image' => $oss_result['data']['url']
                    ];
                    $upd_data = [
                        'success' => json_encode($ref_msg),
                        'status' => 2,
                        'endtime' => time()
                    ];
                    $res = $tools_order_info->save($upd_data);
                    $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
//                    $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
                }else
                {
                    exception($oss_result['msg']);
//                    $this->error($oss_result['msg']);
                }
            }else
            {
                exception($result['msg']);
//                $this->error($result['msg']);
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success();
//        dump($result);
    }

    /**
     * 创建AI写真
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createPortraitByTx()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $utils = new Utils();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_xiezhen')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        Db::startTrans();
        try {

            $ModelId = time().'_'.$this->oemId.'_'.$user_info['id'];
            $content = [
                'image' => $params['userUrls'][0],
                'ModelId' => $ModelId
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => 'ai_xiezhen',
            ];
            $tools_order_result = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            $send_params = [
                'ModelId' => $ModelId,
                'BaseUrl' => $params['userUrls'][0],
                'TrainMode' => 2
            ];
            $result_upd = $utils->createPortrait($send_params,$this->oemId);
//            dump($result_upd);
            if($result_upd['code'] == 1)
            {
                $draw_params = [
                    'ModelId' => $ModelId,
                    'StyleId' => $params['aiStyleId'],
                    'ImageNum' => 1,
                    'Definition' => 'hdpro'
                ];
                $draw_result = $utils->drawPortraitJob($draw_params,$this->oemId);
                if($draw_result['code'] == 1)
                {
                    $upd_data = [
                        'success' => $draw_result['data'],
                    ];
                    $res = $tools_order_info->save($upd_data);
                }else
                {
                    exception($draw_result['msg']);
                }
            }else
            {
                exception($result_upd['msg']);
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success('创建成功');
    }

    /**
     * 获取写真风格列表
     * @return void
     */
    public function getPortraitStyleByTx()
    {
        $style_list = [
            [
                'id' => 1,
                'value' => 'zhengjian_female',
                'title' => '证件照-长发(女)',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/953b24b2-7a06-4796-9e6a-ea33f630324f.png'
            ],
            [
                'id' => 2,
                'value' => 'zhengjian_shorthair_female',
                'title' => '证件照-短发(女)',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/b5dcf0bf-2dcb-41ba-ae37-bb168a07a432.png'
            ],
            [
                'id' => 3,
                'value' => 'zhengjian_male',
                'title' => '证件照-男',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/e552bf1d-7bef-43f6-86a3-efd4df01197f.png'
            ],
            [
                'id' => 4,
                'value' => 'princess_female',
                'title' => '公主(女)',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/ac7e916d-a4a4-46cc-a460-aa5c34360074.png'
            ],
            [
                'id' => 5,
                'value' => 'elderly_male',
                'title' => '中老年(男)',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/f98d627c-cb27-4580-a100-e9d57244db5d.png'
            ],
            [
                'id' => 6,
                'value' => 'elderly_female',
                'title' => '中老年(女)',
                'imageUrl' => 'https://cloudcache.tencentcs.com/qcloud/ui/static/static_source_business/c27155cf-2076-4b66-9710-be396eab28bc.png'
            ],
        ];
        $this->success('ok',$style_list);
    }


    /**
     * 提取文案
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getTextOcrByTx()
    {
        $params = $this->request->post();
        $user_info = $this->auth->getUserinfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $tools_info = $tools_model->where('rule','ai_tiqu')->where('admin_id',$this->oemId)->find();
        if(!$tools_info)
        {
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price'])
//        {
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        $data = '';
        Db::startTrans();
        try {
            $content = [
                'image' => $params['url']
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => 'ai_tiqu',
            ];
            $tools_order_result = $tools_order_model->save($add);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $url = $params['url'];
            $type = $params['type'];
            $utils = new Utils();
            if($type == 'image')
            {
                //图片提取
                $send_params = [
                    'LanguageType' => 'auto',
                    'ImageUrl' => $url
                ];
                $img_result = $utils->getTextFromImgByTx($send_params,$this->oemId);
                if($img_result['code'] == 1)
                {
                    $text = json_decode($img_result['data'],true);
                    $ref_text = '';
                    foreach ($text['TextDetections'] as $key => $value)
                    {
                        $ref_text .= $value['DetectedText'];
                    }
                    $data = $ref_text;
                    $success = [
                        'text' => $ref_text
                    ];
                    $upd_data = [
                        'status' => 2,
                        'endtime' => time(),
                        'success' => json_encode($success)
                    ];
                    $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                }else
                {
                    $upd_data = [
                        'status' => 3,
                        'endtime' => time(),
                        'error' => $img_result['msg']
                    ];
                    $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                    exception($img_result['msg']);
                }
            }elseif($type == 'audio')
            {
                //音频提取
                $send_params = [
                    'EngSerViceType' => '16k_zh',
                    'SourceType' => 0,
                    'VoiceFormat' => 'mp3',
                    'Url' => $url
                ];
                $audio_result = $utils->getTextFromAudioByTx($send_params,$this->oemId);
                if($audio_result['code'] == 1)
                {
                    $text = json_decode($audio_result['data'],true);
                    $ref_text = $text['Result'];

                    $data = $ref_text;
                    $success = [
                        'text' => $ref_text
                    ];
                    $upd_data = [
                        'status' => 2,
                        'endtime' => time(),
                        'success' => json_encode($success)
                    ];
                    $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                }else
                {
                    $upd_data = [
                        'status' => 3,
                        'endtime' => time(),
                        'error' => $audio_result['msg']
                    ];
                    $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                    exception($audio_result['msg']);
                }
            }else
            {
                $url = $params['url'];
                // 正则表达式：匹配https链接，允许末尾有或没有斜杠
                $pattern = '/https?:\/\/[^\s\'"]*\/?/';
                preg_match($pattern, $url, $matches);
                
                // 输出提取结果
                if (!empty($matches[0])) {
                    $url = $matches[0];
                } else {
                    exception('未找到链接');
                }
                //视频提取
                $video_result = $utils->getTextFromVideoByOther($url,$this->oemId);
                if($video_result['code'] == 1)
                {
//                    dump($video_result['data']);
                    $res = json_decode($video_result['data'],true);
//                    dump($res);
                    $TaskId = $res['Data']['TaskId'];

                    while (true)
                    {
                        $tq_res = $utils->describeTaskStatus($TaskId,$this->oemId);
                        $res_data = json_decode($tq_res['data'],true);
                        if($tq_res['code'] == 1)
                        {
//                            dump($res_data);
//                            dump($tq_res['data']);
//                            dump(json_decode($tq_res['data'],true));
                            if($res_data['Data']['Status'] == 2 && $res_data['Data']['StatusStr'] == 'success')
                            {
                                $success = [
                                    'text' => $res_data['Data']['Result'],
                                ];
                                $data = $res_data['Data']['Result'];
                                $upd_data = [
                                    'status' => 2,
                                    'endtime' => time(),
                                    'success' => json_encode($success)
                                ];
                                $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                                break;
                            }
                            if($res_data['Data']['Status'] == 3 && $res_data['Data']['StatusStr'] == 'failed')
                            {
                                $upd_data = [
                                    'status' => 3,
                                    'endtime' => time(),
                                    'error' => $tq_res['data']
                                ];
                                $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                                exception($tq_res['msg']);
                                break;
                            }
                        }else
                        {
                            $upd_data = [
                                'status' => 3,
                                'endtime' => time(),
                                'error' => $tq_res['data']
                            ];
                            $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                            exception($tq_res['msg']);
                            break;
                        }
                        sleep(1);
                    }
                }else
                {
                    $upd_data = [
                        'status' => 3,
                        'endtime' => time(),
                        'error' => $video_result['msg']
                    ];
                    $tools_order_model->save($upd_data,['id'=>$tools_order_info['id']]);
                    exception($video_result['msg']);
                }
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok',$data);
    }

    /**
     * 创建照片高清化
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createRefineImage()
    {
        $user_info = $this->auth->getUserinfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_gaoqing')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        $params = $this->request->post();
        $content = [
            'image' => $params['url'],
        ];
        $data = '';
        Db::startTrans();
        try {
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule']
            ];
            $tools_order_result = $tools_order_model->save($add);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $send_tx_params = [
                'InputUrl' => $params['url'],
                'RspImgType' => 'url'
            ];
            $utils = new Utils();
            $result = $utils->refineImage($send_tx_params,$this->oemId);
            if($result['code'] == 1)
            {
                $image_arr = json_decode($result['data'],true);
                //把图片上传到oss
                // 本地临时文件名，用于保存从网址下载的文件
                $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$user_info['id'].'.jpg';
                // 下载网址文件到本地临时文件
                file_put_contents('.'.$localTempFile, file_get_contents($image_arr['ResultImage']));
                $oss_result = $utils->uploadOss($localTempFile,$this->oemId);
                if($oss_result['code'] == 1)
                {
                    $data = $oss_result['data']['url'];
                    $ref_msg = [
                        'image' => $oss_result['data']['url']
                    ];
                    $upd_data = [
                        'success' => json_encode($ref_msg),
                        'status' => 2,
                        'endtime' => time()
                    ];
                    $res = $tools_order_info->save($upd_data);
//                    $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
                }else
                {
                    exception($oss_result['msg']);
//                    $this->error($oss_result['msg']);
                }
//                dump($image_arr);

            }else
            {
                exception($result['msg']);
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok',$data);


    }

    /**
     * 生成图片风格化
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createImgStyle()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $utils = new Utils();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_style')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if (!$tools_type){
            $this->error('算力不足');
        }
        $data = '';
        Db::startTrans();
        try {
            $content = [
                'image' => $params['url'],
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => 'ai_style',
            ];
            $tools_order_result = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $send_params = [
                'InputUrl' => $params['url'],
                'Styles.N' => $params['style'],
                'RspImgType' => 'url'
            ];
            $result_upd = $utils->createImgStyleByTx($send_params,$this->oemId);
//            dump($result_upd);
            if($result_upd['code'] == 1)
            {

//                dump($result_upd);
//                dump(json_decode($result_upd['data'],true));
                $img_arr = json_decode($result_upd['data'],true);
                //把图片上传到oss
                // 本地临时文件名，用于保存从网址下载的文件
                $localTempFile = '/uploads/'.date('Ymd',time()).'/'.time().'_'.$user_info['id'].'.jpg';
                // 下载网址文件到本地临时文件
                file_put_contents('.'.$localTempFile, file_get_contents($img_arr['ResultImage']));
                $oss_result = $utils->uploadOss($localTempFile,$this->oemId);
                if($oss_result['code'] == 1)
                {
                    $ref_msg = [
                        'image' => $oss_result['data']['url']
                    ];
                    $data = $oss_result['data']['url'];
                    $upd_data = [
                        'success' => json_encode($ref_msg),
                        'status' => 2,
                        'endtime' => time()
                    ];
                    $res = $tools_order_info->save($upd_data);
                }else
                {
                    exception($oss_result['msg']);
//                    $this->error($oss_result['msg']);
                }

            }else
            {
                exception($result_upd['msg']);
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('创建成功',$data);
    }

    /**
     * 创建图片跳舞
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createImageAnimate()
    {
        $user_info = $this->auth->getUserInfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $utils = new Utils();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_tiaowu')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        $data = '';
        Db::startTrans();
        try {
            $content = [
                'image' => $params['url'],
                'style' => $params['style'],
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => 'ai_tiaowu',
            ];
            $tools_order_result = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $send_params = [
                'ImageUrl' => $params['url'],
                'TemplateId' => $params['style'],
            ];
            $result_upd = $utils->createImageAnimateJob($send_params,$this->oemId);
//            dump($result_upd);
            if($result_upd['code'] == 1)
            {
                $upd_data = [
                    'success' => $result_upd['data'],
                    'status' => 1
                ];
                $res = $tools_order_info->save($upd_data);

            }else
            {
                exception($result_upd['msg']);
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('创建成功');
    }


    /**
     * 获取声音复刻文本
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createTextAudioByTx()
    {
        $utils = new Utils();
        $result = $utils->getTrainingText(5,$this->oemId);
        if($result['code'] == 1)
        {
            $TrainingTextList = json_decode($result['data'],true);
            $data = [
                'text' => $TrainingTextList['Data']['TrainingTextList'][0]['Text'],
                'TextId' => $TrainingTextList['Data']['TrainingTextList'][0]['TextId']
            ];
            $this->success('ok',$data);
        }else
        {
            $this->error('工具暂时无法使用');
        }
    }
    
    //新版获取录音文案
    public function createTextAudio()
    {
        $list = [
                '明天要赶早班车出差，我不仅定好了六点的闹钟，还特意检查了两遍，又在手机备忘录写了提醒，可千万不能因为赖床错过行程。',
                '清晨推开窗，第一缕阳光温柔地洒在脸上，空气中弥漫着青草混着泥土的芬芳，这样的好天气，最适合沿着河边慢慢散步，听听鸟鸣，放空思绪。',
                '你网购的书籍已经显示签收了，快递小哥放在小区门口的智能柜里，取件码我发你手机上了，记得吃完晚饭下楼取，别让书在柜子里过夜。',
                '这道几何证明题乍一看复杂，其实只要连接辅助线，再利用三角形全等的定理，一步步推导，答案就会清晰呈现出来。',
                '终于等到周末了！和闺蜜约好了去新开的那家影院看悬疑片，听说那里的巨幕和杜比音效超绝，光是想象就兴奋得不行。',
                '灶上炖着排骨呢，先用大火煮开，撇去浮沫，再转小火慢炖半小时，记得时不时去厨房看看，别让汤汁烧干了。',
                '天气预报说今晚降温十度，衣柜里那件厚毛衣我已经拿出来挂在衣架上了，明天出门一定要穿上，再围条围巾，保暖可不能马虎。',
                '你的平板电脑电量只剩 15% 了，我把充电器放在茶几上了，充上电后正好可以边追剧边等电量充满，这样就不耽误晚上用了。',
                '新入手的降噪耳机简直是通勤神器！戴上它，地铁里的嘈杂声瞬间消失，播放喜欢的音乐，仿佛拥有了独属于自己的静谧小世界。',
                '阳台上的绿萝叶子都蔫了，该好好打理一下了。先浇透水，把发黄的叶子剪掉，再搬到通风散光的地方，过几天就能恢复生机。'
            ];
        $key = rand(0,count($list)-1);
        $data = $list[$key];
        $this->success('ok',$data);
    }

    /**
     * 音频检测
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     *
     */
    public function createAudioEnter()
    {
        $user_info = $this->auth->getUserinfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_yinse')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if (!$tools_type){
            $this->error('算力不足');
        }
        $params = $this->request->post();
//        dump($params);
        $content = [
            'title' => $params['title'],
            'audio' => $params['url'],
        ];
        Db::startTrans();
        try {
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule']
            ];
            $tools_order_result = $tools_order_model->save($add);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            // 获取 MP3 文件内容
            $mp3Content = file_get_contents($params['url']);

            if ($mp3Content === false) {
                $error = [
                    'text' => '无法获取音频链接'.$params['url']
                ];
                $upd_data = [
                    'error' => json_encode($error),
                    'status' => 3,
                    'endtime' => time()
                ];
                $tools_order_info->save($upd_data);
                // 处理获取文件失败的情况
                exception('无法获取音频文件');
            } else {
                // 将 MP3 文件内容转换为 Base64 编码
                $base64Encoded = base64_encode($mp3Content);
                $send_tx_params = [
                    'TextId' => $params['TextId'],
                    'AudioData' => $base64Encoded,
                    'TypeId' => 2,
                    'Codec' => 'wav',
                    'TaskType' => 5
                ];
//                dump($send_tx_params);
//                die();
                $utils = new Utils();
                $result = $utils->detectEnvAndSoundQuality($send_tx_params,$this->oemId);
                if($result['code'] == 1)
                {
//                    dump($result['data']);
//                    dump(json_decode($result['data'],true));
                    $arr = json_decode($result['data'],true);
//                    dump($arr);
                    if($arr['Data']['AudioId'] )
                    {
                        $audioId = $arr['Data']['AudioId'];
                        $send_tx_params1 = [
                            'SessionId' => time().$user_info['id'],
                            'VoiceName' => $params['title'],
                            'VoiceGender' => (int)$params['VoiceGender'],
                            'VoiceLanguage' => 1,
                            'AudioIdList' => [$audioId],
                            'TaskType' => 5,
                            'EnableVoiceEnhance' =>1
                        ];
                        $tast_result = $utils->CreateVRSTask($send_tx_params1,$this->oemId);
//                        file_put_contents('aaaaa.txt',json_encode($tast_result));
                        if($tast_result['code'] == 1)
                        {
                            $arr = json_decode($tast_result['data'],true);
//                            dump($arr);
                            $task_id = $arr['Data']['TaskId'];
                            $success = [
                                'TaskId' => $task_id,
                                'RequestId' => $arr['RequestId'],
                            ];
//                            dump($success);
                            $upd_data = [
                                'success' => json_encode($success)
                            ];
                            $res = $tools_order_info->save($upd_data);

                        }else
                        {
                            $error = [
                                'text' => '克隆提交失败'.$tast_result['msg']
                            ];
                            $upd_data = [
                                'error' => json_encode($error),
                                'status' => 3,
                                'endtime' => time()
                            ];
                            $res = $tools_order_info->save($upd_data);
                            exception($tast_result['msg']);
                        }

                    }else
                    {
                        $error = [
                            'text' => $result['msg']
                        ];
                        $upd_data = [
                            'error' => json_encode($error),
                            'status' => 3,
                            'endtime' => time()
                        ];
                        $res = $tools_order_info->save($upd_data);
                        exception('检测失败,注意噪音、漏读、错读、多读。');
                    }


                }else
                {
                    $error = [
                        'text' => '音质检测失败'.$result['msg']
                    ];
                    $upd_data = [
                        'error' => json_encode($error),
                        'status' => 3,
                        'endtime' => time()
                    ];
                    $res = $tools_order_info->save($upd_data);
                    exception($result['msg']);
                }
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok');
    }
    
    
    public function createAudio()
    {
        $user_info = $this->auth->getUserinfo();
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_yinse')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
//        if($user_info['score'] < $tools_info['price']){
//            $this->error('算力不足');
//        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if (!$tools_type){
            $this->error('算力不足');
        }
        $params = $this->request->post();
//        dump($params);
        $content = [
            'title' => $params['title'],
            'audio' => $params['url'],
        ];
        Db::startTrans();
        try {
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule']
            ];
            $tools_order_result = $tools_order_model->save($add);
//            $users_model->where('id',$user_info['id'])->setDec('score',$tools_info['price']);
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok');
    }


    /**
     * 生成音频
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createAudioByTx()
    {
        $user_info = $this->auth->getUserinfo();
        $params = $this->request->post();
        $send_tx_params = [
            'text' => $params['text'],
            'FastVoiceType' => $params['FastVoiceType'],
        ];
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $utils = new Utils();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_yinpin')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        Db::startTrans();
        try {
            $content = [
                'text' => $params['text'].';使用音频为'.$params['FastVoiceType'],
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule']
            ];
            $res = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            $result = $utils->createAudio($send_tx_params,$this->oemId);
//            dump($result);
            if($result['code'] == 1)
            {
                $success = [
                    'text' => $params['title'],
                    'audio' => $result['data']['url']
                ];
                $upd_data = [
                    'success' => json_encode($success),
                    'status' => 2,
                    'endtime' => time()
                ];
            }else
            {
                $error = [
                    'text' => $result['msg']
                ];
                $upd_data = [
                    'error' => json_encode($error),
                    'status' => 3,
                    'endtime' => time()
                ];
            }
            $tools_order_info->save($upd_data);
            Db::commit();
        }catch (\Exception $e)
        {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('生成成功');
    }
    
    public function createNewAudio()
    {
        $user_info = $this->auth->getUserinfo();
        $params = $this->request->post();
        $send_tx_params = [
            'text' => $params['text'],
            'asr_format_audio_url' => $params['asr_format_audio_url'],
            'reference_audio_text' => $params['reference_audio_text'],
        ];
        $tools_model = new \app\common\model\Tools();
        $tools_order_model = new Toolsorder();
        $users_model = new Users();
        $utils = new Utils();
        $params = $this->request->post();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_yinpin')->find();
        if(!$tools_info){
            $this->error('工具暂时无法使用');
        }
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('次数或算力不足');
        }
        Db::startTrans();
        try {
            $content = [
                'text' => $params['text'],
                'asr_format_audio_url' => $params['asr_format_audio_url'],
                'reference_audio_text' => $params['reference_audio_text'],
                
            ];
            $add = [
                'name' => $tools_info['name'],
                'users_id' => $user_info['id'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule']
            ];
            $res = $tools_order_model->save($add);
            Db::commit();
        }catch (\Exception $e)
        {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('生成成功');
    }

    /**
     * 提交视频合成
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createVideo()
    {
        $user_info = $this->auth->getUserinfo();
        $params = $this->request->post();
        $tools_order_model = new Toolsorder();
        $tools_model = new \app\common\model\Tools();
        $tools_info = $tools_model->where('admin_id',$this->oemId)->where('rule','ai_shipin')->find();
        $inspect = new Inspect();
        $tools_type = $inspect->usersScoreOrNum($tools_info['rule'],$user_info['id'],$this->oemId);
        if(!$tools_type){
            $this->error('算力不足');
        }
        Db::startTrans();
        try {
            $content = [
                'text' => json_encode($params),
            ];
            $add = [
                'users_id' => $user_info['id'],
                'name' => $tools_info['name'],
                'tools_id' => $tools_info['id'],
                'paydata' => 1,
                'price' => $tools_info['price'],
                'content' => json_encode($content),
                'status' => 1,
                'oem_id' => $user_info['oem_id'],
                'agent_id' => $user_info['agent_id'],
                'tools_rule' => $tools_info['rule'],
                'power' => $user_info['power']
            ];
            $res = $tools_order_model->save($add);
            $tools_order_info = $tools_order_model->where('id',$tools_order_model->id)->find();
            $inspect->updateUsersScoreOrNum($tools_type,$user_info['id'],$tools_info['id']);
            Db::commit();
        }catch (\Exception $e)
        {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('提交成功');

    }

}