<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\User;
use app\common\controller\Backend;
use app\common\model\Attachment;
use fast\Date;
use think\Db;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {
        $where = [];
        if($this->auth->admin_type == 2)
        {
            $where['oem_id'] = $this->auth->id;
        }elseif($this->auth->admin_type == 3)
        {
            $where['agent_id'] = $this->auth->id;
        }
        $users_model = new \app\common\model\Users();
        //总用户数量
        $count_users = $users_model->where($where)->count();
        //今日新增
        $starttime = Date::unixtime('day');
        $day_count_users = $users_model->where($where)->where('createtime','gt',$starttime)->count();
        //今日收款
        $vip_order_model = new \app\common\model\Viporder();
        $day_count_money = $vip_order_model->where($where)->where('createtime','gt',$starttime)->where('status','in','1,2,3')->sum('price');
        //今日工具使用次数
        $tools_order_model = new \app\common\model\Toolsorder();
        $day_count_tools = $tools_order_model->where($where)->where('createtime','gt',$starttime)->count();
        //获取七日内收款金额
        $column = [];
        $starttime = Date::unixtime('day', -6);
        $endtime = Date::unixtime('day', 0, 'end');
        $vip_order_list = Db("viporder")->where('paytime', 'between time', [$starttime, $endtime])->where($where)
            ->field('SUM(price) AS count_money, DATE_FORMAT(FROM_UNIXTIME(createtime), "%Y-%m-%d") AS create_date')
            ->group('create_date')
            ->select();
        for ($time = $starttime; $time <= $endtime;) {
            $column[] = date("Y-m-d", $time);
            $time += 86400;
        }

        $paylist = array_fill_keys($column, 0);

        foreach ($vip_order_list as $k => $v) {
            $paylist[$v['create_date']] = $v['count_money'];
        }



        //七日内退款金额
        $column_refund = [];
        $vip_order_refund_list = Db("viporder")->where('endtime', 'between time', [$starttime, $endtime])->where('status',3)->where($where)
            ->field('SUM(price) AS count_money, DATE_FORMAT(FROM_UNIXTIME(createtime), "%Y-%m-%d") AS create_date')
            ->group('create_date')
            ->select();
        for ($time = $starttime; $time <= $endtime;) {
            $column_refund[] = date("Y-m-d", $time);
            $time += 86400;
        }

        $refund_list = array_fill_keys($column_refund, 0);

        foreach ($vip_order_refund_list as $k => $v) {
            $refund_list[$v['create_date']] = $v['count_money'];
        }



        //七日内工具使用次数
        $column_tools = [];
        $tools_order_list = Db("toolsorder")->where('createtime', 'between time', [$starttime, $endtime])->where($where)
            ->field('COUNT(*) AS nums, DATE_FORMAT(FROM_UNIXTIME(createtime), "%Y-%m-%d") AS create_date')
            ->group('create_date')
            ->select();
        for ($time = $starttime; $time <= $endtime;) {
            $column_tools[] = date("Y-m-d", $time);
            $time += 86400;
        }

        $tools_list = array_fill_keys($column_tools, 0);

        foreach ($tools_order_list as $k => $v) {
            $tools_list[$v['create_date']] = $v['nums'];
        }


        //工具使用次数
        $tools_starttime = Date::unixtime('day',-30);
        $tools_endtime = Date::unixtime('day', 0, 'end');
        $tools_order_num = $tools_order_model->where($where)->where('createtime', 'between', [$tools_starttime, $tools_endtime])->field('count(*) as nums,tools_id')->group('tools_id')->select();
        $tools_model = new \app\common\model\Tools();
//        $tolls_all = $tools_model->where($where)->where('status','in','1,2,3')->count();
        $tools_num = [];
        foreach ($tools_order_num as $k => $v) {
            $tools_num[$k]['value'] = $v['nums'];
            $tools_num[$k]['name'] = Db::name('tools')->where(['id' => $v['tools_id']])->value('name');
        }
        $time_text = date("Y-m-d", $tools_starttime).' - '.date("Y-m-d", $tools_endtime);





        $admin_model = new \app\admin\model\Admin();
        $agent_qrcode = $admin_model->where('id',$this->auth->id)->value('agent_qrcode');

        $this->view->assign([
            'count_users'         => $count_users,
            'day_count_users'         => $day_count_users,
            'day_count_money'         => $day_count_money,
            'day_count_tools'         => $day_count_tools,
            'agent_qrcode'        => $agent_qrcode,
        ]);
//        dump($tools_num);
//        die();

        $this->assignconfig('column', array_keys($paylist));
        $this->assignconfig('paydata', array_values($paylist));
        $this->assignconfig('refunddata', array_values($refund_list));
        $this->assignconfig('toolsorderdata', array_values($tools_list));
        $this->assignconfig('toolsnumdata', array_values($tools_num));
        $this->assignconfig('time_text', $time_text);

        return $this->view->fetch();
    }

    public function tools()
    {
        if($this->request->isAjax()){
            $type = $this->request->get("type") ?? 1;
            //工具使用次数
            $where = [];
            if($this->auth->admin_type == 2)
            {
                $where['oem_id'] = $this->auth->id;
            }elseif($this->auth->admin_type == 3)
            {
                $where['agent_id'] = $this->auth->id;
            }
            $tools_order_model = new \app\common\model\Toolsorder();
            if($type == 1)
            {
                $tools_starttime = Date::unixtime('day');
            }elseif($type == 2)
            {
                $tools_starttime = Date::unixtime('day',-1);
            }elseif ($type == 3)
            {
                $tools_starttime = Date::unixtime('day', -6);
            }elseif ($type == 4)
            {
                $tools_starttime = Date::unixtime('day', -30);
            }
            $tools_endtime = Date::unixtime('day', 0, 'end');

            $tools_order_num = $tools_order_model->where($where)->where('createtime', 'between', [$tools_starttime, $tools_endtime])->field('count(*) as nums,tools_id')->group('tools_id')->select();

            $tools_num = [];
            foreach ($tools_order_num as $k => $v) {
                $tools_num[$k]['value'] = $v['nums'];
                $tools_num[$k]['name'] = Db::name('tools')->where(['id' => $v['tools_id']])->value('name');
            }
            $time_text = date("Y-m-d", $tools_starttime).' - '.date("Y-m-d", $tools_endtime);
            return json(['tools_num' => $tools_num,'time_text' => $time_text]);

        }
    }

}
