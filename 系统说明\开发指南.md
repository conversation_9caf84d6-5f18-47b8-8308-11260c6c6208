# 开发指南

## 快速上手

### 1. 环境准备

**系统要求**：
- PHP 7.4+ (推荐 PHP 8.0+)
- MySQL 5.7+ 或 MySQL 8.0+
- Nginx 1.18+ 或 Apache 2.4+
- Composer 2.0+
- Node.js 14+ (用于前端构建)

**开发工具推荐**：
- IDE: PhpStorm 或 VS Code
- 数据库管理: Navicat 或 phpMyAdmin
- API测试: Postman 或 Apifox
- 版本控制: Git

### 2. 项目部署

#### 2.1 克隆项目
```bash
git clone [项目地址]
cd szr-xcx.sanliankj.com.cn
```

#### 2.2 安装依赖
```bash
# 安装PHP依赖
composer install

# 安装前端依赖（如果需要）
npm install
```

#### 2.3 配置环境
```bash
# 复制环境配置文件
cp .env.sample .env

# 编辑配置文件
vim .env
```

#### 2.4 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE szrxcx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构（如果有SQL文件）
mysql -u username -p szrxcx < database.sql
```

#### 2.5 设置权限
```bash
# 设置目录权限
chmod -R 755 ./
chmod -R 777 runtime/
chmod -R 777 public/uploads/
```

### 3. 开发环境配置

#### 3.1 开启调试模式
```php
// .env文件
[app]
debug = true
trace = true
```

#### 3.2 配置虚拟主机
```nginx
# Nginx配置
server {
    listen 80;
    server_name local.szr-xcx.com;
    root /path/to/project/public;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 开发规范

### 1. 代码规范

#### 1.1 命名规范
```php
// 类名：大驼峰命名
class UserController extends Api {}

// 方法名：小驼峰命名
public function getUserInfo() {}

// 变量名：小驼峰命名
$userInfo = [];

// 常量：全大写+下划线
const MAX_RETRY_COUNT = 3;

// 数据库表名：小写+下划线
fa_users, fa_tools_order

// 字段名：小写+下划线
user_id, create_time
```

#### 1.2 注释规范
```php
/**
 * 获取用户信息
 * @param int $userId 用户ID
 * @return array 用户信息数组
 * @throws \Exception 当用户不存在时抛出异常
 */
public function getUserInfo($userId)
{
    // 查询用户信息
    $userInfo = $this->model->where('id', $userId)->find();
    
    if (!$userInfo) {
        throw new \Exception('用户不存在');
    }
    
    return $userInfo->toArray();
}
```

#### 1.3 错误处理
```php
// 统一错误处理
try {
    $result = $this->processData($data);
    $this->success('操作成功', $result);
} catch (\Exception $e) {
    // 记录错误日志
    Log::error('处理失败: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'data' => $data
    ]);
    
    $this->error('操作失败: ' . $e->getMessage());
}
```

### 2. 数据库开发规范

#### 2.1 模型定义
```php
<?php
namespace app\common\model;

use think\Model;

class Users extends Model
{
    // 表名
    protected $name = 'users';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'integer';
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    // 字段类型转换
    protected $type = [
        'viptime' => 'timestamp',
        'logintime' => 'timestamp',
    ];
    
    // 关联关系
    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id');
    }
}
```

#### 2.2 查询规范
```php
// 推荐写法
$users = $this->model
    ->where('status', 1)
    ->where('oem_id', $this->oemId)
    ->order('createtime', 'desc')
    ->paginate(10);

// 避免写法
$users = Db::query("SELECT * FROM fa_users WHERE status = 1");
```

### 3. API开发规范

#### 3.1 控制器结构
```php
<?php
namespace app\api\controller;

use app\common\controller\Api;

class User extends Api
{
    // 无需登录的方法
    protected $noNeedLogin = ['wxLogin'];
    
    // 无需权限验证的方法
    protected $noNeedRight = '*';
    
    /**
     * 初始化
     */
    public function _initialize()
    {
        parent::_initialize();
        // 控制器初始化逻辑
    }
    
    /**
     * 获取用户信息
     */
    public function getUserInfo()
    {
        $userInfo = $this->auth->getUserInfo();
        $this->success('获取成功', $userInfo);
    }
}
```

#### 3.2 响应格式
```php
// 成功响应
$this->success('操作成功', $data);

// 失败响应
$this->error('操作失败', null, 400);

// 响应格式
{
    "code": 1,
    "msg": "操作成功",
    "time": "1640995200",
    "data": {}
}
```

### 4. 第三方服务集成规范

#### 4.1 服务封装
```php
// application/service/TencentService.php
class TencentService
{
    private $config;
    
    public function __construct($oemId)
    {
        $this->config = $this->getOemConfig($oemId, 'tencent');
    }
    
    /**
     * AI绘画
     */
    public function textToImage($params)
    {
        try {
            $client = $this->createClient('aiart');
            $request = new TextToImageLiteRequest();
            $request->fromJsonString(json_encode($params));
            $response = $client->TextToImageLite($request);
            
            return $this->formatResponse(true, '成功', $response);
        } catch (\Exception $e) {
            return $this->formatResponse(false, $e->getMessage());
        }
    }
    
    private function formatResponse($success, $message, $data = null)
    {
        return [
            'success' => $success,
            'message' => $message,
            'data' => $data
        ];
    }
}
```

## 常见开发任务

### 1. 新增API接口

#### 1.1 创建控制器方法
```php
/**
 * 新增工具
 */
public function addTool()
{
    $params = $this->request->post();
    
    // 参数验证
    $validate = new \think\Validate([
        'name' => 'require|max:100',
        'rule' => 'require|max:50',
        'price' => 'require|float|egt:0'
    ]);
    
    if (!$validate->check($params)) {
        $this->error($validate->getError());
    }
    
    // 业务逻辑
    $toolsModel = new Tools();
    $params['admin_id'] = $this->oemId;
    $params['createtime'] = time();
    
    $result = $toolsModel->save($params);
    
    if ($result) {
        $this->success('添加成功');
    } else {
        $this->error('添加失败');
    }
}
```

#### 1.2 添加路由（如需要）
```php
// application/route.php
return [
    'api/tools/add' => 'api/tools/addTool',
];
```

### 2. 新增数据表

#### 2.1 创建迁移文件
```sql
-- 创建表结构
CREATE TABLE `fa_new_table` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '名称',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
    `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
    `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新表';
```

#### 2.2 创建模型文件
```php
<?php
namespace app\common\model;

use think\Model;

class NewTable extends Model
{
    protected $name = 'new_table';
    protected $autoWriteTimestamp = 'integer';
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
}
```

### 3. 集成新的第三方服务

#### 3.1 安装SDK
```bash
composer require vendor/package-name
```

#### 3.2 添加配置
```php
// 在fa_oemconfig表中添加配置项
INSERT INTO fa_oemconfig (name, group, title, type, value, admin_id) VALUES
('service_key', 'newservice', '服务密钥', 'string', '', 1),
('service_secret', 'newservice', '服务密钥', 'string', '', 1);
```

#### 3.3 封装服务类
```php
// application/service/NewService.php
class NewService
{
    private $config;
    
    public function __construct($oemId)
    {
        $utils = new Utils();
        $this->config = $utils->getOemConfig($oemId, 'newservice');
    }
    
    public function callApi($params)
    {
        // 调用第三方API
        // 返回统一格式结果
    }
}
```

## 调试技巧

### 1. 日志调试
```php
// 记录调试日志
Log::info('调试信息', ['data' => $data]);

// 记录错误日志
Log::error('错误信息', [
    'error' => $e->getMessage(),
    'file' => $e->getFile(),
    'line' => $e->getLine()
]);

// 查看日志
tail -f runtime/log/202501/01.log
```

### 2. 数据库调试
```php
// 开启SQL日志
'database' => [
    'debug' => true,
    'sql_explain' => true,
];

// 查看执行的SQL
Db::getLastSql();
```

### 3. API调试
```php
// 输出调试信息
dump($data);
var_dump($data);

// 返回调试数据
$this->success('调试', [
    'params' => $this->request->param(),
    'config' => $this->oemConfig,
    'user' => $this->auth->getUserInfo()
]);
```

## 部署指南

### 1. 生产环境部署
```bash
# 1. 关闭调试模式
# .env文件
[app]
debug = false
trace = false

# 2. 清理缓存
rm -rf runtime/cache/*
rm -rf runtime/temp/*

# 3. 设置权限
chmod -R 755 ./
chmod -R 777 runtime/
chmod 600 .env

# 4. 重启服务
systemctl reload nginx
systemctl reload php-fpm
```

### 2. 性能优化
```php
// 开启OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000

// 开启缓存
'cache' => [
    'type' => 'redis',
    'host' => '127.0.0.1',
    'port' => 6379,
];
```

## 常见问题

### 1. 权限问题
```bash
# 文件权限不足
chmod -R 777 runtime/
chmod -R 777 public/uploads/

# 数据库连接失败
# 检查.env配置和数据库服务状态
```

### 2. 第三方服务调用失败
```php
// 检查配置是否正确
$config = $utils->getOemConfig($oemId, 'tencent');
var_dump($config);

// 检查网络连接
curl -I https://aiart.tencentcloudapi.com
```

### 3. 支付回调问题
```php
// 检查回调URL是否可访问
// 检查证书文件是否存在
// 查看支付日志
```

## 扩展开发

### 1. 插件开发
- 基于FastAdmin插件机制
- 独立的配置和数据
- 支持在线安装卸载

### 2. 主题定制
- 修改前端模板文件
- 自定义CSS样式
- 响应式设计

### 3. API扩展
- 遵循RESTful规范
- 统一的响应格式
- 完善的错误处理

---

**更新日期**：2025年1月  
**文档版本**：v1.0
