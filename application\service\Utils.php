<?php

namespace app\service;

use app\common\model\Admin;
use app\common\model\Oemconfig;
use app\common\model\Tools;
use app\common\model\Users;
use app\common\model\Vip;
use app\common\model\Viporder;
use fast\Http;
use OSS\Core\OssException;
use OSS\Credentials\StaticCredentialsProvider;
use OSS\OssClient;
use Qcloud\Cos\Client;
use Ratchet\Client\Connector;
use Ratchet\Client\WebSocket;
use React\EventLoop\Factory;
use React\EventLoop\Loop;
use TencentCloud\Aiart\V20221229\AiartClient;
use TencentCloud\Aiart\V20221229\Models\ImageToImageRequest;
use TencentCloud\Aiart\V20221229\Models\QueryDrawPortraitJobRequest;
use TencentCloud\Aiart\V20221229\Models\QueryDrawPortraitJobResponse;
use TencentCloud\Aiart\V20221229\Models\RefineImageRequest;
use TencentCloud\Aiart\V20221229\Models\SubmitDrawPortraitJobRequest;
use TencentCloud\Aiart\V20221229\Models\UploadTrainPortraitImagesRequest;
use TencentCloud\Asr\V20190614\AsrClient;
use TencentCloud\Asr\V20190614\Models\CreateRecTaskRequest;
use TencentCloud\Asr\V20190614\Models\DescribeTaskStatusRequest;
use TencentCloud\Asr\V20190614\Models\SentenceRecognitionRequest;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Profile\ClientProfile;
use TencentCloud\Common\Profile\HttpProfile;
use TencentCloud\Hunyuan\V20230901\HunyuanClient;
use TencentCloud\Hunyuan\V20230901\Models\TextToImageLiteRequest;
use TencentCloud\Ocr\V20181119\Models\GeneralBasicOCRRequest;
use TencentCloud\Ocr\V20181119\OcrClient;
use TencentCloud\Vclm\V20240523\Models\DescribeImageAnimateJobRequest;
use TencentCloud\Vclm\V20240523\Models\SubmitImageAnimateJobRequest;
use TencentCloud\Vclm\V20240523\VclmClient;
use TencentCloud\Vrs\V20200824\Models\CreateVRSTaskRequest;
use TencentCloud\Vrs\V20200824\Models\DescribeVRSTaskStatusRequest;
use TencentCloud\Vrs\V20200824\Models\DetectEnvAndSoundQualityRequest;
use TencentCloud\Vrs\V20200824\Models\GetTrainingTextRequest;
use TencentCloud\Vrs\V20200824\VrsClient;
use think\Db;

class Utils
{


    /**
     * 腾讯云实时语音合成
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createAudio($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        $text = $params['text'];
        $voiceType = 200000000; // 语音类型，根据腾讯云文档选择合适的值
        $sessionId = uniqid();
        $timestamp = time();
        $expired = $timestamp + 3600; // 签名过期时间，可根据需求调整

        // 生成签名
        $stringToSign = "GETtts.cloud.tencent.com/stream_ws?Action=TextToStreamAudioWS&AppId=".$tx_config['tx_appid']."&Codec=mp3&Expired=$expired&FastVoiceType=".$params['FastVoiceType']."&SecretId=".$tx_config['tx_secretid']."&SessionId=$sessionId&Text=$text&Timestamp=$timestamp&VoiceType=$voiceType";


//        file_put_contents('yuyin.txt', $stringToSign.PHP_EOL, FILE_APPEND);
        $sign = base64_encode(hash_hmac('sha1', $stringToSign, $tx_config['tx_secretkey'], true));

//        file_put_contents('yuyin.txt', $sign.PHP_EOL, FILE_APPEND);
        // 构建请求 URL
        $requestUrl = "wss://tts.cloud.tencent.com/stream_ws?Action=TextToStreamAudioWS&AppId=".$tx_config['tx_appid']."&Codec=mp3&Expired=$expired&SecretId=".$tx_config['tx_secretid']."&SessionId=$sessionId&Signature=" . urlencode($sign) . "&Text=" . urlencode($text) . "&Timestamp=$timestamp&VoiceType=$voiceType&FastVoiceType=".$params['FastVoiceType'];

//        file_put_contents('yuyin.txt', $requestUrl.PHP_EOL, FILE_APPEND);
        // 创建事件循环
        $loop = Loop::get();
        $connector = new Connector($loop);

        // 初始化 MP3 数据
        $mp3Data = '';

        // 初始化文件路径变量
        $mp3FilePath = null;
        $dirPath = './uploads/xcx/'.$admin_id.'/audio/';
        if (!is_dir($dirPath)) {
            mkdir($dirPath, 0777, true); // 第三个参数为 true 表示递归创建父目录
        }
        $connector($requestUrl)->then(function (WebSocket $conn) use (&$mp3Data, $loop, &$mp3FilePath,$admin_id) {
            $conn->on('message', function ($msg) use (&$mp3Data, $conn, $loop) {
                $mp3Data .= $msg;
                // file_put_contents('yuyin.txt', $mp3Data.PHP_EOL, FILE_APPEND);
                // 这里可以根据返回的数据判断是否结束，例如根据特定的结束标识
                // 假设返回的最后一个消息包含 "END" 表示结束
                if (strpos($msg, 'END')!== false) {
                    $conn->close();
                    $loop->stop();
                }
            });

            $conn->on('close', function ($code = null, $reason = null) use (&$mp3Data, &$mp3FilePath,$admin_id) {
                // 将 MP3 数据保存到文件
                $mp3FilePath = '/uploads/xcx/'.$admin_id.'/audio/output_' . uniqid() . '.mp3';
                file_put_contents('.'.$mp3FilePath, $mp3Data);
            });
        }, function ($e) use ($loop) {
//            return json(['error' => "连接失败: {$e->getMessage()}"]);
            return $this->returnData(0,"连接失败: {$e->getMessage()}");
        });

        $loop->run();

        // 返回 MP3 文件路径
        if ($mp3FilePath) {
            $result = $this->uploadOss($mp3FilePath,$admin_id);
            if ($result['code'] == 1) {
                return $this->returnData(1,'ok',['url' => $result['data']['url']]);
            }else
            {
                return $this->returnData(0,$result['msg']);
            }
        } else {
            return $this->returnData(0,'生成音频失败');
        }
    }

    /**
     * 查询声音复刻结果
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function DescribeVRSTaskStatus($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vrs.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VrsClient($cred, "", $clientProfile);
            $req = new DescribeVRSTaskStatusRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->DescribeVRSTaskStatus($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage().';ID:'.$e->getRequestId());
        }
    }

    public function getTextFromImgByTx($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("ocr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new OcrClient($cred, "", $clientProfile);
            $req = new GeneralBasicOCRRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->GeneralBasicOCR($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage());
        }
    }

    public function getTextFromAudioByTx($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("asr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AsrClient($cred, "", $clientProfile);
            $req = new SentenceRecognitionRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->SentenceRecognition($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage());
        }



    }

    /**
     * 提取视频链接地址
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getTextFromVideoByOther($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $other_config = $this->getOemConfig($admin_id,'other');
        if(!$other_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        // $send_params = [
        //     'appid' => $other_config['hh_appid'],
        //     'link' => $params
        // ];
        // $url = 'https://watermark-api.hlyphp.top/Watermark/Index';
        $send_params = [
            'url' => $params
        ];
        // $url = "https://xzdx.top/api/duan/";
        $url = "https://qsy.ruoj.cn/api/dsp/32E9840DD281A0ED2A908DAA604587AC057F7B34D2D0FBA49F/202037118/?url=".$params;
        $result = Http::get($url);
        // dump($result);
        $res = json_decode($result,true);
        if($res['code'] == 200 && $res['data']['video'])
        {
//            dump($res['data']['videoSrc']);
//            return $this->returnData(1,'提取成功',$res['data']);
            try {
                $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
                // 实例化一个http选项，可选的，没有特殊需求可以跳过
                $httpProfile = new HttpProfile();
                $httpProfile->setEndpoint("asr.tencentcloudapi.com");

                // 实例化一个client选项，可选的，没有特殊需求可以跳过
                $clientProfile = new ClientProfile();
                $clientProfile->setHttpProfile($httpProfile);
                // 实例化要请求产品的client对象,clientProfile是可选的
                $client = new AsrClient($cred, "", $clientProfile);
                $req = new CreateRecTaskRequest();
                $send_tx_params = [
                    'EngineModelType' => '16k_zh',
                    'ChannelNum' => 1,
                    'ResTextFormat' => 0,
                    'SourceType' => 0,
                    'Url' => $res['data']['video'],
                ];
                $req->fromJsonString(json_encode($send_tx_params));

                // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
                $resp = $client->CreateRecTask($req);
//                $res = json_decode($resp->toJsonString(),true);

                return $this->returnData(1,'ok',$resp->toJsonString());
            }catch (TencentCloudSDKException $e)
            {
                return $this->returnData(0,$e->getMessage());
            }
        }else
        {
            return $this->returnData(0,'提取失败'.$res['code']);
        }
    }

    /**
     * 查询提取文案进度
     * @param $task_id
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public  function describeTaskStatus($task_id,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("asr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AsrClient($cred, "", $clientProfile);
            $req = new DescribeTaskStatusRequest();
            $send_tx_params = [
                'TaskId' => $task_id,
            ];
            $req->fromJsonString(json_encode($send_tx_params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->DescribeTaskStatus($req);
//                $res = json_decode($resp->toJsonString(),true);

            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage());
        }
    }

    public function refineImage($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }


        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("aiart.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AiartClient($cred, "ap-guangzhou", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new RefineImageRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->RefineImage($req);
//                $res = json_decode($resp->toJsonString(),true);

            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage());
        }
    }

    /**
     * 获取声音复刻文本
     * @param $TaskType
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getTrainingText($TaskType,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }


        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vrs.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VrsClient($cred, "", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new GetTrainingTextRequest();
            $send_tx_params = [
                'TaskType' => $TaskType
            ];
            $req->fromJsonString(json_encode($send_tx_params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->GetTrainingText($req);
//                $res = json_decode($resp->toJsonString(),true);

            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
            return $this->returnData(0,$e->getMessage());
        }
    }

    /**
     * 声音复刻音质检测
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function detectEnvAndSoundQuality($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }

        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vrs.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VrsClient($cred, "", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new DetectEnvAndSoundQualityRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->DetectEnvAndSoundQuality($req);
//                $res = json_decode($resp->toJsonString(),true);

            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
//            dump($e);
            return $this->returnData(0,$e->getMessage().';ID:'.$e->getRequestId());
        }
    }


    /**
     * 提交声音复刻任务
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function CreateVRSTask($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }

        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vrs.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VrsClient($cred, "", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new CreateVRSTaskRequest();

            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
            $resp = $client->CreateVRSTask($req);
//                $res = json_decode($resp->toJsonString(),true);

            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e)
        {
//            dump($e);
            return $this->returnData(0,$e->getMessage().';ID:'.$e->getRequestId());
        }
    }


    /**
     * AI图像风格化
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */

    public function createImgStyleByTx($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("aiart.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AiartClient($cred, "ap-guangzhou", $clientProfile);
            $req = new ImageToImageRequest();
            $req->fromJsonString(json_encode($params));
            $resp = $client->ImageToImage($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }


    /**
     *
     * 创建图片跳舞
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createImageAnimateJob($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vclm.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VclmClient($cred, "ap-guangzhou", $clientProfile);
            $req = new SubmitImageAnimateJobRequest();
            $req->fromJsonString(json_encode($params));
            $resp = $client->SubmitImageAnimateJob($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }


    public function getVideoStatus($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("vclm.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new VclmClient($cred, "ap-guangzhou", $clientProfile);
            $req = new DescribeImageAnimateJobRequest();
//            dump($params);
            $req->fromJsonString(json_encode($params));
            $resp = $client->DescribeImageAnimateJob($req);
//            dump($resp);
//            dump($resp->toJsonString());
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }


    /**
     * 上传AI写真图片
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createPortrait($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("aiart.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AiartClient($cred, "ap-guangzhou", $clientProfile);
            $req = new UploadTrainPortraitImagesRequest();
            $req->fromJsonString(json_encode($params));
            $resp = $client->UploadTrainPortraitImages($req);
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }

    /**
     * 提交AI写真任务
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function drawPortraitJob($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("aiart.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AiartClient($cred, "ap-guangzhou", $clientProfile);
            $req = new SubmitDrawPortraitJobRequest();
            $req->fromJsonString(json_encode($params));
            $resp = $client->SubmitDrawPortraitJob($req);
//            dump($resp);
//            dump($resp->toJsonString());
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }

    /**获取AI写真任务结果
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getStatusPortraitJob($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'], $tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("aiart.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AiartClient($cred, "ap-guangzhou", $clientProfile);
            $req = new QueryDrawPortraitJobRequest();
//            dump($params);
            $req->fromJsonString(json_encode($params));
            $resp = $client->QueryDrawPortraitJob($req);
//            dump($resp);
//            dump($resp->toJsonString());
            return $this->returnData(1,'ok',$resp->toJsonString());
        }catch (TencentCloudSDKException $e) {
            return  $this->returnData(0,$e->getMessage());
        }
    }


    /**
     * 根据VIP默认工具次数给用户添加次数。添加算力
     * @param $vip_id
     * @param $users_id
     * @param $oem_id
     * @return array
     */
    public  function addUsersToolsNumScoreByVip($vip_id,$users_id,$oem_id,$vip_order_id)
    {
        Db::startTrans();
        try {
            $vip_order_info = Viporder::get($vip_order_id);
            if(!$vip_order_info)
            {
                exception('订单不存在');
            }
            if ($vip_order_info['oem_id'] != $oem_id && $oem_id != 1)
            {
                exception('无权操作该订单');
            }
            $vip_info = Vip::get($vip_id);
            if(!$vip_info)
            {
                exception('VIP信息不存在');
            }
            if($vip_info['admin_id'] != $oem_id)
            {
                exception('无权操作VIP');
            }
            $users_info = Users::get($users_id);
            if(!$users_info)
            {
                exception('用户信息不存在');
            }
            if($users_info['oem_id'] != $oem_id && $oem_id != 1)
            {
                exception('无权操作用户');
            }
            $tools_num_model = new \app\common\model\Toolsnum();
            $vip_tools_num = $tools_num_model->where('vip_id',$vip_id)->select();
            if($vip_tools_num)
            {
                foreach ($vip_tools_num as $vip_tools)
                {
                    $users_tools_info = $tools_num_model->where('users_id',$users_id)->where('tools_id',$vip_tools['tools_id'])->find();
                    if(!$users_tools_info)
                    {
                        $add_data = [
                            'users_id' => $users_id,
                            'tools_id' => $vip_tools['tools_id'],
                            'num' => $vip_tools['num'],
                        ];
                        $res = $tools_num_model->save($add_data);
                    }else
                    {
                        $res = $tools_num_model->where('users_id',$users_id)->where('tools_id',$vip_tools['tools_id'])->setInc('num',$vip_tools['num']);
                    }
                    if(!$res)
                    {
                        exception('数据处理错误');
                    }
                }
            }
            if($vip_info['score'] > 0)
            {
                $res = $users_info->setInc('score',$vip_info['score']);
                if(!$res)
                {
                    exception('增加算力错误');
                }
            }
            $res = $vip_order_info->save(['status' => 2]);
            if(!$res)
            {
                exception('订单状态修改失败');
            }
            Db::commit();
            return $this->returnData();

        } catch (\Exception $e)
        {
            Db::rollback();
            return $this->returnData(0,$e->getMessage());
        }
    }

    public function uploadOss($file,$admin_id,$type='image')
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $oss_config = $this->getOemConfig($admin_id,'aliyun');

//        dump($oss_config);
        if(!$oss_config)
        {
            return $this->returnData(0,'暂无配置');
        }


//        $oss_config = [
//            'access_key_id' => 'LTAI5tFhzgNi3YCGLhY3AikH',
//            'access_key_secret' => '******************************',
//            'endpoint' => 'oss-cn-shanghai.aliyuncs.com',
//            'bucket' => 'bjcai',
//            'urls' => 'https://bjcai.oss-cn-shanghai.aliyuncs.com'
//        ];

        try {
            $provider = new StaticCredentialsProvider(
                $oss_config['access_key_id'],
                $oss_config['access_key_secret']
            );
            $endpoint = $oss_config['oss_endpoint'];
            $endpoint_arr = explode('.',$endpoint);
            $region_arr = explode('-',$endpoint_arr[0]);
            $region = $region_arr[1].'-'.$region_arr[2];
            $config = array(
                "provider" => $provider,
                "endpoint" => $endpoint,
                "signatureVersion" => OssClient::OSS_SIGNATURE_VERSION_V4,
                "region"=> $region
            );
            $ossClient = new OssClient($config);
            $obj_path = 'szr/'.$admin_id.$file;
            $file_path = '.'.$file;
            $result = $ossClient->uploadFile($oss_config['oss_bucket'], $obj_path, $file_path);
            $return = [
                'url' => $oss_config['oss_urls'].'/'.$obj_path,
            ];
            return $this->returnData(1,'ok',$return);
        } catch (OssException $e) {
            return $this->returnData(0,$e->getMessage());
        }
    }

    /**
     * 腾讯云OSS
     * @param $file
     * @param $admin_id
     * @param $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function uploadOss1($file,$admin_id,$type='image')
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $oss_config = $this->getOemConfig($admin_id,'tencent');
        if(!$oss_config)
        {
            return $this->returnData(0,'暂无配置');
        }

//        $oss_config = [
//            'secretId' => 'AKIDUp03tVyEG5i8MMRK1Ys1xb8ctxqW1KQu',
//            'secretKey' => 'KPIxObdR5YptkOp46yc4JK8j8MPFuMek',
//            'bucket' => 'szrxcx-1251881935',
//            'urls' => 'https://szrxcx-1251881935.cos.ap-guangzhou.myqcloud.com',
//            'region' => 'ap-guangzhou'
//        ];

        try {
            $config = array(
                "region"=> $oss_config["region"],
                "scheme"=> "http",
                "credentials" => [
                    "secretId" => $oss_config["secretId"],
                    "secretKey" => $oss_config["secretKey"]
                ]
            );
            $ossClient = new Client($config);
            $obj_path = 'szr/'.$admin_id.$file;
            $file_path = '.'.$file;
            $params = [
                'Bucket' => $oss_config['bucket'],
                'Key' => $obj_path,
                'Body' => fopen($file_path, 'rb')
            ];
            $result = $ossClient->putObject($params);
            $result = [
                'url' => $oss_config['urls'].'/'.$obj_path,
            ];
            return $this->returnData(1,'ok',$result);
        } catch (\Exception $e) {
            return $this->returnData(0,$e->getMessage());
        }
    }

    public function chatAi($text,$admin_id,$modelAi = 'deepseek-v3')
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $bd_config = $this->getOemConfig($admin_id,'baiduyun');
        if(!$bd_config)
        {
            return $this->returnData(0,'暂无配置');
        }

        $chat_url = 'https://qianfan.baidubce.com/v2/chat/completions';
        $header = [
            CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'appid: '.$bd_config['bd_v2_appid'],
            'Authorization: Bearer '.$bd_config['bd_v2_apikey']
        ]];
//        $header = [
//            CURLOPT_HTTPHEADER => [
//            'Content-Type: application/json',
//            'appid: app-t5qzAn8u',
//            'Authorization: Bearer bce-v3/ALTAK-5s7dH5R8mm17wqy9xEgB2h/bfba5f43f9802ee2accb6bcb81de387c550504d9'
//        ]];
        $params = [
            'model' => $modelAi,
            'messages' => $text
        ];
        $result = Http::post($chat_url,json_encode($params),$header);
//        dump($result);
//        dump(json_decode($result,true));
        $res = json_decode($result,true);
        if(isset($res['error']) || !$res)
        {
            //返回错误，保存错误信息
//            array(2) {
//                        ["error"] => array(3) {
//                            ["code"] => string(17) "invalid_iam_token"
//                            ["message"] => string(17) "invalid_iam_token"
//                            ["type"] => string(21) "invalid_request_error"
//              }
//              ["id"] => string(13) "as-nx70gzgj7b"
//            }
            return $this->returnData(0,$res);
        }else
        {
//            array(6) {
//                    ["id"] => string(13) "as-vartrf13y2"
//                    ["object"] => string(15) "chat.completion"
//                    ["created"] => int(1744638437)
//                    ["model"] => string(11) "deepseek-v3"
//                    ["choices"] => array(1) {
//                        [0] => array(4) {
//                            ["index"] => int(0)
//                            ["message"] => array(2) {
//                                ["role"] => string(9) "assistant"
//                                ["content"] => string(416) "**【iPhone 15 惊艳登场】**
//        全新iPhone 15系列，搭载A16仿生芯片，性能再突破！超视网膜XDR显示屏，色彩更生动；4800万像素主摄，影像能力飙升。轻盈设计，航空级铝金属边框，配色时尚。续航升级，全天候耐用。更快的5G网络，智能岛交互革新。科技与美学交融，定义未来体验。**#iPhone15# 更Pro，更强大！** （99字）"
//              }
//              ["finish_reason"] => string(4) "stop"
//                            ["flag"] => int(0)
//            }
//          }
//          ["usage"] => array(3) {
//                        ["prompt_tokens"] => int(14)
//                        ["completion_tokens"] => int(103)
//                        ["total_tokens"] => int(117)
//          }
//        }
            return $this->returnData(1,'ok',$res);
        }
//        dump(json_decode($result,true));
    }

    /**
     * 文本纠错百度API接口
     * @param $text
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function nlp($text,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $bd_config = $this->getOemConfig($admin_id,'baiduyun');
        if(!$bd_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        $bd_access_token = $this->getBdToken($bd_config,$admin_id);
//        $bd_access_token = '24.e822c5345b54ffd6e82c6479ed0f2788.2592000.1747224649.282335-118493347';
        if(!$bd_access_token)
        {
            return $this->returnData(0,'接口配置错误');
        }


        $api_url = 'https://aip.baidubce.com/rpc/2.0/nlp/v1/ecnet?charset=UTF-8&access_token='.$bd_access_token;
        $params = [
            'text' => $text,
        ];
        $result = Http::post($api_url,json_encode($params));
        $res = json_decode($result,true);
        if(isset($res['error_code']))
        {
            return $this->returnData(0,$res['error_code'].$res['error_msg']);
        }else
        {
            return $this->returnData(1,'ok',$res);
        }
    }


    /**
     * 查询更新百度云token
     * @param $bd_config
     * @param $admin_id
     * @return false|mixed
     */
    public function getBdToken($bd_config,$admin_id)
    {

        if(isset($bd_config["bd_expires_in"]) && $bd_config["bd_expires_in"] > time())
        {
            return $bd_config['bd_access_token'];
        }
        $get_token_url = "https://aip.baidubce.com/oauth/2.0/token";
        $param = [
            'grant_type' => 'client_credentials',
            'client_id' => $bd_config["bd_apikey"],
            'client_secret' => $bd_config["bd_secretkey"],
        ];
//        $param = [
//            'grant_type' => 'client_credentials',
//            'client_id' => 'o9KFhMZ1bZSm7h6diESG0C1L',
//            'client_secret' => 'l0809uX8nvGUcsOjW7LC5Zz6MtroNH4V',
//        ];
        $result = Http::post($get_token_url,$param);
        $result = json_decode($result,true);
        if(isset($result["error"]) || !$result)
        {
            return false;
        }
        $where_upd = [
            'admin_id' => $admin_id,
            'group' => 'baiduyun',
        ];
        $oem_config_model = new OemConfig();
        $res = $oem_config_model->where($where_upd)->where('name','bd_access_token')->update(['value' => $result['access_token']]);
        $res1 = $oem_config_model->where($where_upd)->where('name','bd_expires_in')->update(['value' => time() + $result["expires_in"]]);
        if($res && $res1)
        {
            return $result["access_token"];
        }
        return false;
    }


    /**
     * AI绘画 调用腾讯文生图轻量版
     * @param $params
     * @param $admin_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function TextToImageLiteByTx($params,$admin_id)
    {
        $oem_status = $this->checkOem($admin_id);
        if(!$oem_status)
        {
            return $this->returnData(0,'当前被禁用');
        }
        $tx_config = $this->getOemConfig($admin_id,'tencent');
        if(!$tx_config)
        {
            return $this->returnData(0,'暂无配置');
        }
        try {
            $cred = new Credential($tx_config['tx_secretid'],$tx_config['tx_secretkey']);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("hunyuan.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            $client = new HunyuanClient($cred, "ap-guangzhou", $clientProfile);
            $req = new TextToImageLiteRequest();

            $req->fromJsonString(json_encode($params));
            $resp = $client->TextToImageLite($req);
            return $this->returnData(1,'ok',$resp->getResultImage());
        }catch (TencentCloudSDKException $e){
            return $this->returnData(0,$e->getMessage());
        }
    }

    /**
     * 获取oem用户配置
     * @param $admin_id
     * @param $group
     * @return array|false
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getOemConfig($admin_id,$group='tencent')
    {
        $oem_config_model = new Oemconfig();
        $oem_config_list = $oem_config_model->where('admin_id',$admin_id)->where('group',$group)->select();
        if(!$oem_config_list)
        {
            return false;
        }
        $oss_config = [];
        if(!empty($oem_config_list)){
            foreach ($oem_config_list as $config){
                $oss_config[$config['name']] = $config['value'];
            }
        }
        return $oss_config;
    }


    /**
     * 检查当前贴牌用户是否正常
     * @param $admin_id
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function checkOem($admin_id)
    {
        $admin_model = new Admin();
        $oem_info = $admin_model->where('id',$admin_id)->find();
        if($oem_info && $oem_info['status'] == 'normal')
        {
            return true;
        }else
        {
            return false;
        }
    }


    /**
     * @param $code
     * @param $msg
     * @param $data
     * @return array
     */
    public  function returnData($code = 1,$msg='OK',$data='')
    {
        return ['code'=>$code,'msg'=>$msg,'data'=>$data];
    }
}