<?php

namespace app\service;

use app\common\model\Sllog;
use app\common\model\Tools;
use app\common\model\Toolsnum;
use app\common\model\Users;
use think\Db;

class Inspect
{

    /**
     * 增加用户默认所有工具次数
     * @param $users_id
     * @param $admin_id
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function addUsersToolsNum($users_id,$admin_id)
    {
        $tools_num_model = new Toolsnum();
        $tools_model = new Tools();
        $tools_list = $tools_model->where('admin_id',$admin_id)->select();
        $add = [];
        foreach ($tools_list as $k=>$tools_info) {
            $add[$k] = [
                'tools_id' => $tools_info['id'],
                'num' => 0,
                'users_id' => $users_id,
            ];
        }
        $tools_num_model->saveAll($add);
    }
    /**
     * 检测用户使用这个工具是要扣次数还是算力
     * @param $tools_rule
     * @param $users_id
     * @param $admin_id
     * @return false|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function usersScoreOrNum($tools_rule,$users_id,$admin_id)
    {
        $tools_model = new Tools();
        $tools_info = $tools_model->where('rule',$tools_rule)->where('admin_id',$admin_id)->find();
        $tools_num_model = new Toolsnum();
        $tools_num = $tools_num_model->where('tools_id',$tools_info['id'])->where('users_id',$users_id)->value('num');
        if($tools_num > 0){
            return 'num';
        }else
        {
            $users_model = new Users();
            $users_score = $users_model->where('id',$users_id)->value('score');
            if($users_score > $tools_info['price']){
                return 'score';
            }else
            {
                return false;
            }
        }
    }


    /**
     * 扣除用户次数或者算力
     * @param $type
     * @param $users_id
     * @param $tools_id
     * @param $admin_id
     * @return false
     * @throws \Exception
     */
    public function updateUsersScoreOrNum($type,$users_id,$tools_id,$jj = 2,$mark = '',$num = 1,$admin_id = 1)
    {
        Db::startTrans();
        try {
            if($type == 'num')
            {

                $tools_num_model = new Toolsnum();
                $users_model = new Users();
                $users_info = $users_model->where('id',$users_id)->find();
                $tools_num = $tools_num_model->where('tools_id',$tools_id)->where('users_id',$users_id)->find();
                $tools_model = new Tools();
                $tools_info = $tools_model->where('id',$tools_id)->find();
                if($jj == 2)
                {
                    //减少
                    $after = $tools_num['num'] - $num;
                }else
                {
                    //增加
                    $after = $tools_num['num'] + $num;
                }
                $add = [
                    'users_id' => $users_id,
                    'tools_id' => $tools_id,
                    'type' => 2,
                    'after' => $after,
                    'before' => $tools_num['num'],
                    'price' => $num,
                    'type_data' => $jj,
                    'oem_id' => $users_info['oem_id'],
                    'agent_id' => $users_info['agent_id'],
                    'mark' => $mark ? $mark : $tools_info['name'],
                ];
                $sl_log_model = new Sllog();
                $res = $sl_log_model->save($add);
                if(!$res)
                {
                    exception('保存日志出错');
                }
                if($jj == 2)
                {
                    //减少
                    $res = $tools_num_model->where('tools_id',$tools_id)->where('users_id',$users_id)->setDec('num',$num);
                }else
                {
                    //增加
                    $res = $tools_num_model->where('tools_id',$tools_id)->where('users_id',$users_id)->setInc('num',$num);
                }
                if(!$res)
                {
                    exception('扣除次数出错');
                }
            }else
            {
                $tools_model = new Tools();
                $tools_info = $tools_model->where('id',$tools_id)->find();
                $users_model = new Users();
                $users_info = $users_model->where('id',$users_id)->find();
                $add = [
                    'users_id' => $users_id,
                    'tools_id' => $tools_id,
                    'type' => 1,
                    'after' => $users_info['score'] - $tools_info['price'],
                    'before' => $users_info['score'],
                    'price' => $tools_info['price'],
                    'type_data' => 2,
                    'oem_id' => $users_info['oem_id'],
                    'agent_id' => $users_info['agent_id'],
                    'mark' => $tools_info['name']
                ];
                $sl_log_model = new Sllog();
                $res = $sl_log_model->save($add);
                if(!$res)
                {
                    exception('保存日志出错');
                }
                $res = $users_model->where('id',$users_id)->setDec('score',$tools_info['price']);
                if(!$res)
                {
                    exception('扣除算力出错');
                }
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            exception($e->getMessage());
        }
        return false;
    }
}