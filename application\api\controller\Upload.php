<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Admin;
use app\common\model\Oemconfig;
use app\common\model\Users;
use app\service\Inspect;
use app\service\Utils;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;
use fast\Random;
use Intervention\Image\ImageManager;
use think\Config;
use think\Validate;

/**
 * 会员接口
 */
class Upload extends Api
{
    protected $noNeedLogin = ['login', 'wxLogin', 'getSets', 'resetpwd', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';


    public function getSets()
    {
        $params = $this->request->param();
        $oem_id = $params['agent_code'];
        $utils = new Utils();
        $ali_config = $utils->getOemConfig($oem_id,'aliyun');
        if(!$ali_config)
        {
            $this->error('找不到OSS配置');
        }
//        dump($ali_config);
       $data = [
            'accessId' => $ali_config['access_key_id'],
            'accessSecret' => $ali_config['access_key_secret'],
            'bucket' => $ali_config['oss_bucket'],
            'endpoint' => $ali_config['oss_endpoint'],
            'url' => $ali_config['oss_urls'],
        ];
        $this->success('', $data,200);
    }





}
