# 系统配置文档

## 配置概述

本系统采用多层次配置管理，包括框架配置、应用配置、环境配置和多租户配置。所有配置都支持环境变量覆盖，便于不同环境的部署和管理。

## 核心配置文件

### 1. 应用主配置 (application/config.php)

**基础配置**：
```php
return [
    // 应用设置
    'app_namespace' => 'app',
    'app_debug' => Env::get('app.debug', false),
    'app_trace' => Env::get('app.trace', false),
    'app_multi_module' => true,
    'default_return_type' => 'html',
    'default_ajax_return' => 'json',
    'default_timezone' => 'PRC',
    
    // 默认模块
    'default_module' => 'index',
    'deny_module_list' => ['common', 'admin'],
    'default_controller' => 'Index',
    'default_action' => 'index',
    
    // URL设置
    'url_route_on' => true,
    'url_route_must' => false,
    'url_convert' => true,
    'url_controller_layer' => 'controller',
];
```

**安全配置**：
```php
// Token设置
'token' => [
    'type' => 'Mysql',
    'key' => '75N2swgGvF3xdk1ZAJmIOCuzStiy8EBV',
    'hashalgo' => 'ripemd160',
    'expire' => 0,
],

// 验证码配置
'captcha' => [
    'codeSet' => '2345678abcdefhijkmnpqrstuvwxyzABCDEFGHJKLMNPQRTUVWXY',
    'fontSize' => 18,
    'useCurve' => false,
    'useZh' => false,
    'imageH' => 40,
    'imageW' => 130,
    'length' => 4,
    'reset' => true
],
```

**FastAdmin配置**：
```php
'fastadmin' => [
    'api_url' => 'https://api.fastadmin.net',
    'version' => '1.6.0.20250331',
    'auto_record_log' => true,
    'addon_pure_mode' => true,
    'cors_request_domain' => 'localhost,127.0.0.1',
],
```

### 2. 数据库配置 (application/database.php)

```php
return [
    // 数据库类型
    'type' => Env::get('database.type', 'mysql'),
    // 服务器地址
    'hostname' => Env::get('database.hostname', '127.0.0.1'),
    // 数据库名
    'database' => Env::get('database.database', 'szrxcx'),
    // 用户名
    'username' => Env::get('database.username', 'szrxcx'),
    // 密码
    'password' => Env::get('database.password', 'SYsTdp76jdfaDbEF'),
    // 端口
    'hostport' => Env::get('database.hostport', ''),
    // 数据库编码
    'charset' => Env::get('database.charset', 'utf8mb4'),
    // 数据库表前缀
    'prefix' => Env::get('database.prefix', 'fa_'),
    // 数据库调试模式
    'debug' => Env::get('database.debug', false),
    // 自动写入时间戳字段
    'auto_timestamp' => false,
    // 时间字段格式
    'datetime_format' => false,
];
```

### 3. 站点配置 (application/extra/site.php)

```php
return [
    'name' => '小程序后台',
    'beian' => '',
    'cdnurl' => '',
    'version' => '1.0.1',
    'timezone' => 'Asia/Shanghai',
    'forbiddenip' => '',
    'languages' => [
        'backend' => 'zh-cn',
        'frontend' => 'zh-cn',
    ],
    'fixedpage' => 'dashboard',
    
    // 邮件配置
    'mail_type' => '1',
    'mail_smtp_host' => 'smtp.qq.com',
    'mail_smtp_port' => '465',
    'mail_smtp_user' => '',
    'mail_smtp_pass' => '',
    'mail_verify_type' => '2',
    'mail_from' => '',
    
    // 附件分类
    'attachmentcategory' => [
        'category1' => '分类一',
        'category2' => '分类二',
        'custom' => '自定义',
    ],
];
```

### 4. 上传配置 (application/extra/upload.php)

```php
return [
    // 上传驱动
    'driver' => 'local',
    // 上传目录
    'uploaddir' => 'uploads',
    // 文件大小限制
    'maxsize' => '10mb',
    // 允许的文件类型
    'mimetype' => 'jpg,png,bmp,jpeg,gif,webp,zip,rar,wav,mp4,mp3,webm',
    // 是否支持分片上传
    'chunking' => true,
    // 分片大小
    'chunksize' => 2097152,
];
```

### 5. 插件配置 (application/extra/addons.php)

```php
return [
    'autoload' => false,
    'hooks' => [
        'config_init' => [
            'summernote',
        ],
    ],
    'route' => [
        '/example$' => 'example/index/index',
        '/example/d/[:name]' => 'example/demo/index',
    ],
    'priority' => [],
    'domain' => '',
];
```

### 6. 队列配置 (application/extra/queue.php)

```php
return [
    'connector' => 'Sync',
    'expire' => 60,
    'default' => 'default',
    'host' => '127.0.0.1',
    'port' => 6379,
    'password' => '',
    'select' => 0,
    'timeout' => 0,
    'persistent' => false,
];
```

## 环境变量配置

### 1. 环境变量文件 (.env)

```ini
[app]
debug = false
trace = false

[database]
hostname = 127.0.0.1
database = szrxcx
username = szrxcx
password = SYsTdp76jdfaDbEF
hostport = 3306
prefix = fa_
charset = utf8mb4
debug = false

[redis]
hostname = 127.0.0.1
port = 6379
password = 
select = 0
```

### 2. 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| app.debug | 应用调试模式 | false |
| app.trace | 应用追踪模式 | false |
| database.hostname | 数据库主机 | 127.0.0.1 |
| database.database | 数据库名 | szrxcx |
| database.username | 数据库用户名 | szrxcx |
| database.password | 数据库密码 | - |
| database.hostport | 数据库端口 | 3306 |
| database.prefix | 表前缀 | fa_ |
| database.charset | 字符集 | utf8mb4 |

## 多租户配置

### 1. OEM配置表结构

多租户配置存储在`fa_oemconfig`表中，支持以下配置分组：

| 分组 | 说明 | 主要配置项 |
|------|------|-----------|
| xcx | 小程序配置 | appid, secret, name, index_bg, index_color |
| wxpay | 微信支付 | mchid, key, notify_url |
| tencent | 腾讯云 | secretid, secretkey |
| aliyun | 阿里云 | access_key_id, access_key_secret, oss_bucket |
| baiduyun | 百度云 | appid, apikey |
| system | 系统配置 | url, domain |

### 2. 配置获取方式

```php
// 在API控制器中获取OEM配置
protected function _initialize()
{
    parent::_initialize();
    
    // 获取OEM ID
    $oem_id = $this->request->get('oem_id') ?? $this->request->param('agent_code');
    $this->oemId = $oem_id;
    
    // 获取OEM配置
    $oem_config_model = new Oemconfig();
    $oem_config_list = $oem_config_model->where('admin_id', $oem_id)->select();
    
    $this->oemConfig = [];
    foreach ($oem_config_list as $config) {
        $this->oemConfig[$config['group']][$config['name']] = $config['value'];
    }
}
```

## Web服务器配置

### 1. Nginx配置示例

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name szr-xcx.sanliankj.com.cn;
    root /www/wwwroot/szr-xcx.sanliankj.com.cn/public;
    index index.php index.html;
    
    # SSL证书配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 隐藏敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ /(application|extend|runtime|vendor)/ {
        deny all;
    }
}
```

### 2. Apache配置示例

```apache
<VirtualHost *:80>
    ServerName szr-xcx.sanliankj.com.cn
    DocumentRoot /www/wwwroot/szr-xcx.sanliankj.com.cn/public
    
    <Directory "/www/wwwroot/szr-xcx.sanliankj.com.cn/public">
        AllowOverride All
        Require all granted
    </Directory>
    
    # 隐藏敏感目录
    <DirectoryMatch "/(application|extend|runtime|vendor)/">
        Require all denied
    </DirectoryMatch>
</VirtualHost>
```

## PHP配置要求

### 1. PHP版本要求
- **最低版本**：PHP 7.4
- **推荐版本**：PHP 8.0+

### 2. 必需扩展
```ini
extension=curl
extension=json
extension=pdo
extension=pdo_mysql
extension=bcmath
extension=gd
extension=mbstring
extension=openssl
extension=zip
```

### 3. 推荐配置
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
max_input_vars = 3000
date.timezone = Asia/Shanghai
```

## 安全配置

### 1. 文件权限
```bash
# 设置目录权限
chmod 755 /www/wwwroot/szr-xcx.sanliankj.com.cn
chmod 755 /www/wwwroot/szr-xcx.sanliankj.com.cn/public
chmod -R 777 /www/wwwroot/szr-xcx.sanliankj.com.cn/runtime
chmod -R 777 /www/wwwroot/szr-xcx.sanliankj.com.cn/public/uploads

# 设置文件权限
chmod 644 /www/wwwroot/szr-xcx.sanliankj.com.cn/public/index.php
chmod 600 /www/wwwroot/szr-xcx.sanliankj.com.cn/.env
```

### 2. 防火墙配置
```bash
# 开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw allow 3306  # MySQL (仅内网)
```

### 3. SSL证书配置
- 使用Let's Encrypt免费证书
- 配置HTTPS重定向
- 启用HSTS安全头

## 监控配置

### 1. 日志配置
```php
// 日志级别
'log' => [
    'type' => 'File',
    'path' => LOG_PATH,
    'level' => ['error', 'warning', 'notice'],
    'file_size' => 2097152,
    'time_format' => 'Y-m-d H:i:s',
],
```

### 2. 性能监控
- 启用慢查询日志
- 配置PHP-FPM状态页
- 设置系统资源监控

## 备份配置

### 1. 数据库备份
```bash
#!/bin/bash
# 每日备份脚本
DATE=$(date +%Y%m%d)
mysqldump -u username -p password szrxcx > /backup/db_$DATE.sql
find /backup -name "db_*.sql" -mtime +7 -delete
```

### 2. 文件备份
```bash
#!/bin/bash
# 文件备份脚本
DATE=$(date +%Y%m%d)
tar -czf /backup/files_$DATE.tar.gz /www/wwwroot/szr-xcx.sanliankj.com.cn
find /backup -name "files_*.tar.gz" -mtime +7 -delete
```

## 部署清单

### 1. 部署前检查
- [ ] PHP版本和扩展
- [ ] 数据库连接
- [ ] 文件权限
- [ ] SSL证书
- [ ] 域名解析

### 2. 配置文件检查
- [ ] .env文件配置
- [ ] 数据库配置
- [ ] 第三方服务配置
- [ ] 证书文件上传

### 3. 功能测试
- [ ] 用户登录
- [ ] API接口
- [ ] 文件上传
- [ ] 支付功能
- [ ] 第三方服务

---

**更新日期**：2025年1月  
**文档版本**：v1.0
