<?php

return array (
  'name' => '小程序后台',
  'beian' => '',
  'cdnurl' => '',
  'version' => '1.0.1',
  'timezone' => 'Asia/Shanghai',
  'forbiddenip' => '',
  'languages' => 
  array (
    'backend' => 'zh-cn',
    'frontend' => 'zh-cn',
  ),
  'fixedpage' => 'dashboard',
  'categorytype' => 
  array (
    'default' => '默认',
    'page' => '单页',
    'article' => '文章',
    'test' => 'Test',
  ),
  'configgroup' => 
  array (
    'basic' => '基础配置',
    'email' => '邮件配置',
    'dictionary' => '字典配置',
    'user' => '会员配置',
    'example' => '示例分组',
    'oemconfig' => '配置字段',
  ),
  'mail_type' => '1',
  'mail_smtp_host' => 'smtp.qq.com',
  'mail_smtp_port' => '465',
  'mail_smtp_user' => '',
  'mail_smtp_pass' => '',
  'mail_verify_type' => '2',
  'mail_from' => '',
  'attachmentcategory' => 
  array (
    'category1' => '分类一',
    'category2' => '分类二',
    'custom' => '自定义',
  ),
  'xcx_appid' => 'xcx',
  'xcx_appsecret' => 'xcx',
  'wx_mchid' => 'wxpay',
  'wx_key' => 'wxpay',
  'cert_file' => 'wxpay',
  'key_file' => 'wxpay',
  'xcx_name' => 'xcx',
  'xcx_logo' => 'xcx',
  'score_rate' => 'rebate',
  'one_level' => 'rebate',
  'two_level' => 'rebate',
  'min_cash' => 'rebate',
  'commission' => 'rebate',
  'poster' => 'rebate',
  'rebate_content' => 'rebate',
  'access_key_id' => 'aliyun',
  'access_key_secret' => 'aliyun',
  'oss_bucket' => 'aliyun',
  'oss_endpoint' => 'aliyun',
  'oss_urls' => 'aliyun',
  'bd_appid' => 'baiduyun',
  'bd_apikey' => 'baiduyun',
  'bd_secretkey' => 'baiduyun',
  'bd_access_token' => 'baiduyun',
  'bd_expires_in' => 'baiduyun',
  'bd_v2_appid' => 'baiduyun',
  'bd_v2_apikey' => 'baiduyun',
  'xcx_index_bj' => 'xcx',
  'xcx_index_color' => 'xcx',
  'tx_secretid' => 'tencent',
  'tx_secretkey' => 'tencent',
  'hh_appid' => 'other',
  'sys_phone' => 'system',
  'sys_url' => 'system',
  'sys_qrcode' => 'system',
  'sys_about' => 'system',
  'sys_users' => 'system',
  'sys_privacy' => 'system',
  'tx_appid' => 'tencent',
  'get_phone' => 'xcx',
);
