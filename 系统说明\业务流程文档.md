# 业务流程文档

## 业务概述

本系统是一个基于微信小程序的AI工具服务平台，支持多租户架构。主要业务流程包括用户注册登录、AI工具使用、VIP会员购买、支付结算等。系统采用三级权限管理：超级管理员 -> OEM -> 代理商。

## 核心业务流程

### 1. 用户注册登录流程

#### 1.1 微信授权登录
```mermaid
sequenceDiagram
    participant 小程序 as 微信小程序
    participant API as API服务
    participant 微信 as 微信服务器
    participant DB as 数据库

    小程序->>微信: wx.login() 获取code
    微信-->>小程序: 返回code
    小程序->>API: /api/user/wxLogin (code, agent_code)
    API->>微信: 调用微信API获取session
    微信-->>API: 返回openid, session_key
    API->>DB: 查询用户是否存在
    alt 用户不存在
        API->>DB: 创建新用户记录
    end
    API->>DB: 生成用户token
    API-->>小程序: 返回用户信息和token
```

**关键代码**：
```php
// application/api/controller/User.php
public function wxLogin()
{
    $code = $this->request->param('code');
    $session_info = $this->xcx_app->auth->session($code);
    
    if (!$session_info || !isset($session_info['openid'])) {
        $this->error('登录失败');
    }
    
    $users_info = $this->model->where('openid', $session_info['openid'])->find();
    if ($users_info) {
        $ret = $this->auth->direct($users_info['id']);
    } else {
        // 创建新用户
        $user_data = [
            'openid' => $session_info['openid'],
            'oem_id' => $this->oemId,
            'agent_id' => $this->oemId,
            'createtime' => time()
        ];
        $this->model->save($user_data);
        $ret = $this->auth->direct($this->model->id);
    }
}
```

#### 1.2 用户信息更新
- 用户可以更新昵称和头像
- 支持获取用户专属二维码用于推广

### 2. AI工具使用流程

#### 2.1 工具调用流程
```mermaid
sequenceDiagram
    participant 用户 as 小程序用户
    participant API as API服务
    participant 权限 as 权限检查
    participant 第三方 as 第三方AI服务
    participant DB as 数据库

    用户->>API: 调用AI工具接口
    API->>权限: 检查用户权限和余额
    alt 权限不足
        权限-->>API: 返回权限不足
        API-->>用户: 提示权限不足
    else 权限充足
        API->>DB: 创建工具订单
        API->>第三方: 调用第三方AI服务
        第三方-->>API: 返回处理结果
        API->>DB: 更新订单状态和结果
        API->>权限: 扣减用户余额/次数
        API-->>用户: 返回处理结果
    end
```

**权限检查逻辑**：
```php
// application/service/Inspect.php
public function usersScoreOrNum($rule, $users_id, $admin_id)
{
    $users_model = new Users();
    $user_info = $users_model->where('id', $users_id)->find();
    
    // 检查VIP状态
    if ($user_info['is_vip'] == 1 && $user_info['viptime'] > time()) {
        return 'vip'; // VIP用户
    }
    
    // 检查工具次数
    $tools_num_model = new Toolsnum();
    $tools_model = new Tools();
    $tools_info = $tools_model->where('admin_id', $admin_id)->where('rule', $rule)->find();
    
    $num = $tools_num_model->where('tools_id', $tools_info['id'])
                           ->where('users_id', $users_id)
                           ->value('num');
    
    if ($num > 0) {
        return 'num'; // 有次数
    }
    
    // 检查积分
    if ($user_info['score'] >= $tools_info['price']) {
        return 'score'; // 积分充足
    }
    
    return false; // 权限不足
}
```

#### 2.2 主要AI工具

**AI绘画**：
- 文生图：根据文字描述生成图片
- 图生图：基于参考图片生成新图片
- 风格转换：改变图片风格

**AI写真**：
- 上传用户照片
- 选择AI风格
- 生成写真照片

**语音识别**：
- 上传音频文件
- 转换为文字
- 支持多种音频格式

**OCR识别**：
- 上传图片
- 识别图片中的文字
- 支持多种证件识别

**语音合成**：
- 输入文字内容
- 选择音色
- 生成语音文件

### 3. VIP会员购买流程

#### 3.1 VIP购买流程
```mermaid
sequenceDiagram
    participant 用户 as 小程序用户
    participant API as API服务
    participant 支付 as 微信支付
    participant DB as 数据库

    用户->>API: 选择VIP套餐
    API->>DB: 创建VIP订单
    API->>支付: 调用微信支付
    支付-->>API: 返回支付参数
    API-->>用户: 返回支付参数
    用户->>支付: 发起支付
    支付->>API: 支付回调通知
    API->>DB: 更新订单状态
    API->>DB: 更新用户VIP状态
    API-->>支付: 返回成功确认
```

**VIP订单创建**：
```php
// application/api/controller/Index.php
public function createVipOrder()
{
    $vip_id = $this->request->param('vip_id');
    $user_info = $this->auth->getUserInfo();
    
    $vip_model = new Vip();
    $vip_info = $vip_model->where('id', $vip_id)->find();
    
    // 创建订单
    $order_data = [
        'order_no' => date('YmdHis') . rand(1000, 9999),
        'users_id' => $user_info['id'],
        'vip_id' => $vip_id,
        'price' => $vip_info['price'],
        'days' => $vip_info['days'],
        'status' => 0,
        'createtime' => time(),
        'oem_id' => $this->oemId
    ];
    
    $viporder_model = new Viporder();
    $viporder_model->save($order_data);
    
    // 调用微信支付
    $wxpay = new WxPay($this->oemId);
    $pay_info = $wxpay->createPayInfo(
        $user_info['openid'],
        'VIP会员-' . $vip_info['name'],
        $order_data['order_no'],
        $vip_info['price'] * 100
    );
    
    $this->success('创建成功', $pay_info);
}
```

### 4. 支付结算流程

#### 4.1 微信支付回调处理
```php
// application/api/controller/Wxpay.php
public function wx_notify()
{
    $wxpay = new WxPay($this->oemId);
    $response = $wxpay->payment->handlePaidNotify(function ($message, $fail) {
        
        // 查找订单
        if (strpos($message['out_trade_no'], 'VIP') !== false) {
            // VIP订单处理
            $viporder_model = new Viporder();
            $order = $viporder_model->where('order_no', $message['out_trade_no'])->find();
            
            if ($order && $order['status'] == 0) {
                // 更新订单状态
                $order->status = 1;
                $order->paytime = time();
                $order->save();
                
                // 更新用户VIP状态
                $users_model = new Users();
                $user = $users_model->where('id', $order['users_id'])->find();
                $user->is_vip = 1;
                $user->viptime = time() + ($order['days'] * 86400);
                $user->save();
            }
        } else {
            // 工具订单处理
            $toolsorder_model = new Toolsorder();
            $order = $toolsorder_model->where('order_no', $message['out_trade_no'])->find();
            
            if ($order && $order['status'] == 0) {
                $order->status = 1;
                $order->save();
            }
        }
        
        return true;
    });
    
    return $response;
}
```

### 5. 多租户管理流程

#### 5.1 租户层级关系
```
超级管理员 (admin_type = 1)
├── OEM租户 (admin_type = 2)
│   ├── 独立的小程序配置
│   ├── 独立的第三方服务配置
│   ├── 独立的用户数据
│   └── 代理商 (admin_type = 3)
│       ├── 共享OEM配置
│       ├── 独立的用户数据
│       └── 独立的佣金结算
```

#### 5.2 数据隔离机制
```php
// 在API控制器中实现数据隔离
protected function _initialize()
{
    parent::_initialize();
    
    // 获取租户ID
    $oem_id = $this->request->get('oem_id') ?? $this->request->param('agent_code');
    $this->oemId = $oem_id;
    
    // 验证租户状态
    $admin_model = new Admin();
    $oem_info = $admin_model->where('id', $oem_id)->find();
    if (!$oem_info || $oem_info['status'] != 'normal') {
        $this->error('当前小程序禁用');
    }
    
    // 加载租户配置
    $this->loadOemConfig($oem_id);
}
```

### 6. 推广分佣流程

#### 6.1 推广关系建立
- 用户通过推广二维码注册时自动建立推广关系
- 推广关系存储在users表的parent_id字段

#### 6.2 佣金计算和发放
- 下级用户消费时自动计算上级佣金
- 佣金记录存储在commissions表
- 支持多级分佣机制

### 7. 系统监控流程

#### 7.1 日志记录
- 所有API调用都记录访问日志
- 第三方服务调用记录详细日志
- 支付订单变更记录操作日志

#### 7.2 异常处理
```php
// 统一异常处理
try {
    // 业务逻辑
    $result = $this->processBusinessLogic();
    $this->success('操作成功', $result);
} catch (Exception $e) {
    // 记录错误日志
    Log::error('业务处理异常: ' . $e->getMessage());
    $this->error('操作失败: ' . $e->getMessage());
}
```

## 业务规则

### 1. 用户权限规则
- VIP用户：无限制使用所有工具
- 普通用户：可使用积分或购买次数包
- 新用户：注册送初始积分

### 2. 计费规则
- 按次计费：每次使用扣除对应积分或次数
- VIP包月：固定费用无限制使用
- 推广返佣：按消费金额的一定比例返佣

### 3. 数据安全规则
- 用户数据按租户隔离
- 敏感信息加密存储
- 定期清理临时文件

## 性能优化

### 1. 缓存策略
- 配置信息缓存
- 用户信息缓存
- 第三方服务结果缓存

### 2. 异步处理
- AI工具处理采用异步模式
- 支付回调异步处理
- 日志写入异步处理

### 3. 负载均衡
- 支持多服务器部署
- 数据库读写分离
- CDN加速静态资源

---

**更新日期**：2025年1月  
**文档版本**：v1.0
