define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            if(Config.show)
                {
                    var delurl = 'viporder/del';
                    var editurl = 'viporder/edit';
                }else
                {
                    var delurl = '';
                    var editurl = '';
                }
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'viporder/index' + location.search,
                    add_url: 'viporder/add',
                    edit_url: editurl,
                    del_url: delurl,
                    multi_url: 'viporder/multi',
                    import_url: 'viporder/import',
                    table: 'viporder',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                showToggle: false,
                showColumns:false,
                searchFormVisible:true,
                columns: [
                    [
                        {field: 'id', title: __('Id'),operate: false},
                        {field: 'order_number', title: __('Order_number'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'wx_order_number', title: __('Wx_order_number'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'vip.name', title: __('Vip_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'users.nickname', title: __('Users_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'price', title: __('Price'), operate:false},
                        {field: 'oem.nickname', title: __('Oem_id'), operate: Config.show ? 'LIKE' : Config.show,visible:Config.show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'agent.nickname', title: __('Agent_id'), operate: Config.agent_show ? 'LIKE' : Config.agent_show,visible:Config.agent_show, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'paytime', title: __('Paytime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'endtime', title: __('Endtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'ajax',
                                    title: __('退款'),
                                    classname: 'btn btn-xs btn-danger  btn-magic btn-ajax',
                                    text:"退款",
                                    confirm: '退款后无法扣除次数，是否继续？',
                                    url: 'viporder/refund',
                                    success: function (data, ret) {
                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status != 0 && row.status != 3)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },
                                {
                                    name: 'ajax',
                                    title: __('确认VIP'),
                                    classname: 'btn btn-xs btn-info  btn-magic btn-ajax',
                                    text:"确认VIP",
                                    confirm: '确认后将增加对应次数，是否继续？',
                                    url: 'viporder/recharge',
                                    success: function (data, ret) {
                                        $(".btn-refresh").click();
                                        // Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
                                        //如果需要阻止成功提示，则必须使用return false;
                                        //return false;
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible:function(row)
                                    {
                                        if(row.status == 1)
                                        {
                                            return true
                                        }else
                                        {
                                            return false;
                                        }
                                    }
                                },


                            ], formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
