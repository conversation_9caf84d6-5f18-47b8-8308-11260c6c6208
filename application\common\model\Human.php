<?php

namespace app\common\model;

use think\Model;


class Human extends Model
{

    

    

    // 表名
    protected $name = 'human';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function oem()
    {
        return $this->belongsTo('Admin', 'oem_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function agent()
    {
        return $this->belongsTo('Admin', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function users()
    {
        return $this->belongsTo('Users', 'users_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
